<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metro2 Report Download</title>
    <link href="/styles/output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
  <div class="flex h-screen">
    <!-- 侧边栏 -->
    <aside class="w-20 lg:w-64 bg-white border-r border-gray-200 flex flex-col justify-between py-6">
      <div>
        <a href="/pages/admin/index.html" class="flex items-center px-4 py-3 hover:bg-gray-100">
          <span class="text-base font-medium text-gray-800">Dashboard</span>
        </a>
      </div>
      <div class="mb-2">
        <a href="/pages/admin_account.html" class="flex items-center px-4 py-3 hover:bg-gray-100">
          <span class="text-base font-medium text-gray-800">Admin account</span>
        </a>
        <button id="logout-btn" class="flex items-center px-4 py-3 hover:bg-gray-100 w-full text-left">
          <span class="text-base font-medium text-gray-800">Logout</span>
        </button>
      </div>
    </aside>
    <!-- 主体内容 -->
    <main class="flex-1 h-screen bg-gray-200 flex items-center justify-center">
      <div class="max-w-[1440px] w-full flex items-center justify-center min-h-screen bg-gray-200">
        <div class="bg-white rounded-2xl shadow p-6 flex flex-col md:flex-row items-center gap-4 w-full max-w-xl border border-gray-200">
          <div class="flex-1 flex flex-col items-center md:items-start min-w-[160px]">
            <span class="text-xl font-bold mb-2">Metro2 Report</span>
          </div>
          <div class="flex-1 flex flex-col items-center md:items-start w-full min-w-[220px]">
            <label class="font-medium mb-2 text-sm">Select Month: <span class="text-gray-500 text-xs">(only up to the previous month)</span></label>
            <input type="month" id="month-select" max="2025-04" class="border rounded px-3 py-1.5 w-56 text-base" value="2025-05" />
          </div>
          <div class="flex items-center justify-center min-w-[120px]">
            <button id="generate-metro2-btn" class="bg-slate-800 hover:bg-slate-900 text-white px-6 py-2 rounded-lg text-base font-medium transition">Generate</button>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Logout Confirmation Modal -->
  <div id="logout-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
      <h2 class="text-lg font-semibold mb-4">Log Out</h2>
      <p class="mb-6">Are you sure you want to log out of your account?</p>
      <div class="flex gap-4 justify-end">
        <button id="cancel-logout-btn" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50">
          Cancel
        </button>
        <button id="confirm-logout-btn" class="px-4 py-2 bg-slate-800 text-white rounded hover:bg-slate-700">
          Confirm Logout
        </button>
      </div>
    </div>
  </div>

  <script>
    // Logout functionality
    async function performLogout() {
      try {
        // Call logout API
        await fetch('/v1/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });
      } catch (error) {
        // Continue with cleanup even if API fails
      }

      // Clear all localStorage data
      localStorage.removeItem('user');
      localStorage.removeItem('accessToken');

      // Clear any other user-related data that might be cached
      if (typeof window !== 'undefined') {
        window.currentUser = null;
      }

      // Redirect to login page
      window.location.href = '/pages/login/';
    }

    // Show logout confirmation modal
    document.getElementById('logout-btn').addEventListener('click', function() {
      document.getElementById('logout-modal').classList.remove('hidden');
    });

    // Cancel logout
    document.getElementById('cancel-logout-btn').addEventListener('click', function() {
      document.getElementById('logout-modal').classList.add('hidden');
    });

    // Confirm logout
    document.getElementById('confirm-logout-btn').addEventListener('click', async function() {
      document.getElementById('logout-modal').classList.add('hidden');
      await performLogout();
    });

    // Close modal when clicking outside
    document.getElementById('logout-modal').addEventListener('click', function(e) {
      if (e.target === this) {
        this.classList.add('hidden');
      }
    });

    // 设置最大月份为上一个月，并设置默认值
    (function() {
      var date = new Date();
      date.setMonth(date.getMonth() - 1);
      var maxMonth = date.toISOString().slice(0, 7);
      var monthInput = document.getElementById('month-select');
      monthInput.max = maxMonth;
      monthInput.value = maxMonth;
    })();

    document.getElementById('generate-metro2-btn').addEventListener('click', async function(event) {
      event.preventDefault();
      var button = this;
      var originalContent = button.innerHTML;
      var monthInput = document.getElementById('month-select');
      var selectedMonth = monthInput.value;
      if (!selectedMonth) {
        alert('Please select a month first');
        return;
      }
      try {
        button.disabled = true;
        button.textContent = 'Generating...';
        var response = await fetch('/v1/metro2/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ month: selectedMonth })
        });
        if (!response.ok) {
          throw new Error('HTTP error! status: ' + response.status);
        }
        var contentDisposition = response.headers.get('Content-Disposition');
        var filename = 'RM-Metro2-' + new Date().toISOString().slice(0,19).replace(/[-:]/g, '').replace('T', '-') + '.txt';
        if (contentDisposition) {
          var matches = /filename=(.+)/.exec(contentDisposition);
          if (matches && matches[1]) {
            filename = matches[1].replace(/["]+/g, '');
          }
        }
        var blob = await response.blob();
        var downloadUrl = window.URL.createObjectURL(blob);
        var link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      } catch (error) {
        alert('Failed to generate Metro2 report: ' + (error.message || 'Unknown error'));
      } finally {
        button.disabled = false;
        button.innerHTML = originalContent;
      }
    });
  </script>
</body>
</html> 