package main

import (
	"net/http"
	"rent_report/config"
	"rent_report/services"

	// "embed"
	_ "rent_report/controller" // Import controllers for side effects (init registration)
	"rent_report/middleware"
	"rent_report/router"
	"rent_report/scheduler"
	"rent_report/utils/encryption"

	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"

	// Stripe
	"os"

	"github.com/stripe/stripe-go/v75"
)

func main() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatal("Failed to load config", "error", err)
	}

	// Load security configuration
	securityConfig := config.LoadSecurityConfig()

	// Stripe 初始化
	stripeKey := os.Getenv("STRIPE_SECRET_KEY")
	if stripeKey == "" {
		stripeKey = goconfig.Config("stripe.secret_key").(string)
	}
	if stripeKey == "" {
		golog.Fatal("Stripe secret key not set. Please set STRIPE_SECRET_KEY env or config.")
	}
	stripe.Key = stripeKey

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		golog.Fatal("Failed to initialize logging", "error", err)
	}

	// Initialize MongoDB
	err := gomongo.InitMongoDB()
	if err != nil {
		golog.Fatal("Failed to initialize MongoDB", "error", err)
	}

	// Initialize encryption
	if err := encryption.InitEncryption(); err != nil {
		golog.Fatal("Failed to initialize encryption", "error", err)
	}

	// Initialize goupload (configuration is now in the main config file)
	if err := config.InitializeGoupload(); err != nil {
		golog.Fatal("Failed to initialize goupload", "error", err)
	}

	// Start scheduler for auto payments
	scheduler.StartAutoPaymentScheduler()

	// Start rent report email batch scheduler
	services.StartRentReportEmailBatchScheduler()

	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()
	r.Use(gin.Recovery())
	r.Use(gin.Logger())
	applySecurityMiddleware(r, securityConfig)
	// Initialize controllers first
	initializeControllers(r)

	// Check if static file serving is enabled
	serveStatic, ok := goconfig.Config("server.serve_static").(bool)
	if !ok {
		// Fallback to developer_mode for backward compatibility
		serveStatic, ok = goconfig.Config("server.developer_mode").(bool)
		if !ok {
			serveStatic = false
		}
	}

	if serveStatic {
		// see: https://github.com/gin-contrib/static?tab=readme-ov-file
		// var server embed.FS
		// Serve static files using gin-contrib/static
		staticPath := goconfig.Config("server.static_path").(string)
		if staticPath == "" {
			staticPath = "web/dist" //TODO: fs.currentDir
		}
		// fs, _ := static.EmbedFolder(server, staticPath)
		// NOTE: this has to be absolute path
		r.Use(static.Serve("/", static.LocalFile(staticPath, false)))
		// r.Use(static.Serve("/", fs))
		golog.Info("Static file serving enabled", "path", staticPath)
	} else {
		// Static files should be served by external server (e.g., Nginx)
		golog.Info("Static file serving disabled - static files should be served by external server")
	}

	// Handle 404 - NoRoute
	r.NoRoute(func(c *gin.Context) {
		golog.Warn("404 Not Found", "path", c.Request.URL.Path)
		c.String(http.StatusNotFound, "404 Not Found")
	})

	// Start server
	port := goconfig.Config("server.port").(string)
	if port == "" {
		port = "8089"
	}

	golog.Info("Server starting", "port", port)
	golog.Info("Server available", "url", "http://localhost:"+port+"/")
	if err := r.Run(":" + port); err != nil {
		golog.Fatal("Failed to start server", "error", err)
	}
}

// applySecurityMiddleware 应用安全中间件
func applySecurityMiddleware(r *gin.Engine, securityConfig *config.SecurityConfig) {
	// 全局安全头
	r.Use(middleware.SecurityHeaders())

	// CORS配置
	r.Use(middleware.CORS(securityConfig))

	// 速率限制（开发环境注释掉）
	// r.Use(middleware.RateLimit(securityConfig))

	// 请求大小限制
	r.Use(middleware.RequestSizeLimit(securityConfig))

	// 输入验证
	r.Use(middleware.InputValidation())

	// 安全日志
	r.Use(middleware.Logging())

	// 安全Cookie
	r.Use(middleware.SecureCookies())
}

func initializeControllers(r *gin.Engine) {
	// Register all controllers
	for _, controller := range router.GetControllers() {
		controller.RegisterRoutes(r)
	}
}
