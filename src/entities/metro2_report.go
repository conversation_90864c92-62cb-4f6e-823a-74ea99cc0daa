package entities

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"rent_report/utils"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// isProduction 检查是否为生产环境
func isProduction() bool {
	env := strings.ToLower(os.Getenv("GIN_MODE"))
	if env == "release" {
		return true
	}

	if strings.ToLower(os.Getenv("ENVIRONMENT")) == "production" {
		return true
	}

	return false
}

// maskSensitiveInfo 遮蔽敏感信息用于日志记录
func maskSensitiveInfo(info string) string {
	if len(info) <= 4 {
		return "****"
	}
	// 只显示前2位和后2位，中间用*替代
	return info[:2] + strings.Repeat("*", len(info)-4) + info[len(info)-2:]
}

// safeMetro2Log 安全的Metro2日志记录函数
func safeMetro2Log(level string, msg string, args ...interface{}) {
	// 在生产环境中，只记录Info级别及以上的日志
	if isProduction() && (level == "debug" || level == "trace") {
		return
	}

	// 过滤敏感信息
	filteredArgs := make([]interface{}, 0, len(args))
	for i := 0; i < len(args); i += 2 {
		if i+1 >= len(args) {
			break
		}

		key := args[i]
		value := args[i+1]

		// 检查是否为敏感字段
		if keyStr, ok := key.(string); ok {
			switch keyStr {
			case "sinNumber", "socialSecurityNumber":
				// SIN号码完全不记录
				continue
			case "phone", "telephoneNumber":
				// 电话号码遮蔽
				if phoneStr, ok := value.(string); ok {
					filteredArgs = append(filteredArgs, key, maskSensitiveInfo(phoneStr))
				} else {
					filteredArgs = append(filteredArgs, key, "****")
				}
			case "firstName", "lastName":
				// 姓名遮蔽（在生产环境中）
				if isProduction() {
					if nameStr, ok := value.(string); ok {
						filteredArgs = append(filteredArgs, key, maskSensitiveInfo(nameStr))
					} else {
						filteredArgs = append(filteredArgs, key, "****")
					}
				} else {
					filteredArgs = append(filteredArgs, key, value)
				}
			default:
				filteredArgs = append(filteredArgs, key, value)
			}
		} else {
			filteredArgs = append(filteredArgs, key, value)
		}
	}

	// 根据级别记录日志
	switch level {
	case "trace":
		// Trace级别的详细调试信息，只在开发环境记录
		if !isProduction() {
			golog.Debug("[TRACE] "+msg, filteredArgs...)
		}
	case "debug":
		golog.Debug(msg, filteredArgs...)
	case "info":
		golog.Info(msg, filteredArgs...)
	case "warn":
		golog.Warn(msg, filteredArgs...)
	case "error":
		golog.Error(msg, filteredArgs...)
	default:
		golog.Info(msg, filteredArgs...)
	}
}

// This struct represents generation records in the database. It does not store the report format itself due to sensitivity of the data.
type Metro2Report struct {
	ID             string                 `json:"id" bson:"_id"`
	UserID         string                 `json:"-" bson:"usrId"`
	OrganizationID string                 `json:"-" bson:"orgId,omitempty"`
	Metro2Data     map[string]interface{} `json:"metro2Data" bson:"metro2Data"`
}

// Metro2GenerationLog 记录Metro2文件生成的详细日志信息
type Metro2GenerationLog struct {
	ID              string             `json:"id" bson:"_id"`
	UserID          string             `json:"userId" bson:"usrId"`
	OrganizationID  string             `json:"-" bson:"orgId,omitempty"`
	ReportMonth     string             `json:"reportMonth" bson:"reportMonth"` // YYYY-MM format
	GeneratedAt     time.Time          `json:"generatedAt" bson:"generatedAt"`
	TotalLeases     int                `json:"totalLeases" bson:"totalLeases"`
	ProcessedLeases []LeaseProcessInfo `json:"processedLeases" bson:"processedLeases"`
	TotalTenants    int                `json:"totalTenants" bson:"totalTenants"`
	TotalPayments   int                `json:"totalPayments" bson:"totalPayments"`
	FileSize        int64              `json:"fileSize" bson:"fileSize"`
	FileName        string             `json:"fileName" bson:"fileName"`
	FileID          string             `json:"fileId,omitempty" bson:"fileId,omitempty"`                 // GridFS file ID (deprecated)
	JSONDataSize    int64              `json:"jsonDataSize" bson:"jsonDataSize"`                         // Size of JSON data
	JSONBackupPath  string             `json:"jsonBackupPath,omitempty" bson:"jsonBackupPath,omitempty"` // Path to JSON backup file
	Metro2FilePath  string             `json:"metro2FilePath,omitempty" bson:"metro2FilePath,omitempty"` // Path to Metro2 txt file
}

// LeaseProcessInfo 记录每个租约的处理信息
type LeaseProcessInfo struct {
	LeaseID         string   `json:"leaseId" bson:"leaseId"`
	PropertyName    string   `json:"propertyName" bson:"propertyName"`
	PropertyAddress string   `json:"propertyAddress" bson:"propertyAddress"`
	TenantCount     int      `json:"tenantCount" bson:"tenantCount"`
	TenantNames     []string `json:"tenantNames" bson:"tenantNames"`
	PaymentCount    int      `json:"paymentCount" bson:"paymentCount"`
	RentAmount      float64  `json:"rentAmount" bson:"rentAmount"`
	CurrentBalance  float64  `json:"currentBalance" bson:"currentBalance"`
	AccountStatus   string   `json:"accountStatus" bson:"accountStatus"`
	StartDate       string   `json:"startDate" bson:"startDate"`
	EndDate         string   `json:"endDate,omitempty" bson:"endDate,omitempty"`
}

func (m *Metro2Report) Create(ctx context.Context) error {
	var reportColl = gomongo.Coll("rr", "metro2_reports")
	if reportColl == nil {
		return fmt.Errorf("metro2_reports collection not initialized")
	}

	scope, err := GetResourceScope(ctx, m.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	m.OrganizationID = scope.OrganizationID

	// Validate required fields
	if m.ID == "" {
		return fmt.Errorf("id")
	}

	_, err = reportColl.InsertOne(ctx, m)
	if err != nil {
		return fmt.Errorf("failed to insert metro2 report: %v", err)
	}

	return nil
}

func GetMetro2Reports(ctx context.Context, reportDate time.Time, userID string) ([]*Metro2Report, error) {
	var reportColl = gomongo.Coll("rr", "metro2_reports")
	if reportColl == nil {
		return nil, fmt.Errorf("metro2_reports collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := bson.M{
		"usrId": userID,
		"orgId": scope.OrganizationID,
	}

	// If reportDate is provided, filter by the month
	if !reportDate.IsZero() {
		startOfMonth := time.Date(reportDate.Year(), reportDate.Month(), 1, 0, 0, 0, 0, reportDate.Location())
		endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Second)
		filter["lastReportDt"] = bson.M{
			"$gte": startOfMonth,
			"$lte": endOfMonth,
		}
	}

	cursor, err := reportColl.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find metro2 reports: %v", err)
	}
	defer cursor.Close(ctx)

	// Initialize reports as empty slice instead of nil
	reports := make([]*Metro2Report, 0)
	if err = cursor.All(ctx, &reports); err != nil {
		return nil, fmt.Errorf("failed to decode metro2 reports: %v", err)
	}

	return reports, nil
}

// Metro2GenerationLog CRUD methods

// Create 创建Metro2生成日志记录
func (m *Metro2GenerationLog) Create(ctx context.Context) error {
	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		return fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	scope, err := GetResourceScope(ctx, m.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	m.OrganizationID = scope.OrganizationID
	m.GeneratedAt = time.Now()

	if m.ID == "" {
		m.ID = utils.GenerateNanoID()
	}

	_, err = coll.InsertOne(ctx, m)
	if err != nil {
		return fmt.Errorf("failed to insert metro2 generation log: %v", err)
	}

	return nil
}

// GetMetro2GenerationLogs 获取Metro2生成日志列表
func GetMetro2GenerationLogs(ctx context.Context, userID string, limit int64) ([]*Metro2GenerationLog, error) {
	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		return nil, fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := bson.M{
		"usrId": userID,
		"orgId": scope.OrganizationID,
	}

	cursor, err := coll.Find(ctx, filter, nil, 0, limit, "-generatedAt") // Sort by generatedAt desc
	if err != nil {
		return nil, fmt.Errorf("failed to find metro2 generation logs: %v", err)
	}
	defer cursor.Close(ctx)

	logs := make([]*Metro2GenerationLog, 0)
	if err = cursor.All(ctx, &logs); err != nil {
		return nil, fmt.Errorf("failed to decode metro2 generation logs: %v", err)
	}

	return logs, nil
}

func GetPayments(ctx context.Context, leaseID string, userID string) ([]*TenantPayment, int64, error) {
	payments, total, err := GetTenantPayments(ctx, leaseID, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tenant payments: %v", err)
	}

	var result []*TenantPayment
	for i := range payments {
		result = append(result, &payments[i])
	}

	return result, total, nil
}

// GetPaymentsUpToDate 获取指定日期及之前的payment记录，用于Metro2报告生成
func GetPaymentsUpToDate(ctx context.Context, leaseID string, userID string, endDate time.Time) ([]*TenantPayment, int64, error) {
	payments, total, err := GetTenantPaymentsUpToDate(ctx, leaseID, userID, endDate)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tenant payments up to date: %v", err)
	}

	var result []*TenantPayment
	for i := range payments {
		result = append(result, &payments[i])
	}

	return result, total, nil
}

// GenerateMetro2FileData generates the Metro2 file data in JSON format for multiple leases
// Returns JSON data and generation log
func GenerateMetro2FileData(ctx context.Context, reportMonth time.Time, userID string) ([]byte, *Metro2GenerationLog, error) {
	// Calculate the start and end of the month
	startOfMonth := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, -1) // Last day of the month

	// Initialize generation log
	generationLog := &Metro2GenerationLog{
		UserID:          userID,
		ReportMonth:     reportMonth.Format("2006-01"),
		ProcessedLeases: make([]LeaseProcessInfo, 0),
	}

	safeMetro2Log("info", "Metro2 Database Query Info",
		"reportMonth", reportMonth.Format("2006-01"),
		"monthStart", startOfMonth.Format("2006-01-02"),
		"monthEnd", endOfMonth.Format("2006-01-02"))

	// Get current user info for permission check
	user, err := GetUserByID(ctx, userID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get user info: %v", err)
	}

	// Get all active leases for the given month, only userID is admin can get
	leases, err := GetLeasesForMonth(ctx, reportMonth, userID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get leases: %v", err)
	}
	if len(leases) == 0 {
		return nil, nil, fmt.Errorf("no leases found for the given month")
	}

	safeMetro2Log("info", "Found leases for Metro2 report", "count", len(leases))

	// Update generation log with basic info
	generationLog.TotalLeases = len(leases)

	// Create Metro2 data structure with header
	metro2Data := map[string]interface{}{
		"header": map[string]interface{}{
			"recordDescriptorWord":     calculateRDW(leases),
			"recordIdentifier":         "HEADER",
			"EquifaxProgramIdentifier": "1101653", // fixed value
			"activityDate":             endOfMonth.Format("2006-01-02T00:00:00Z"),
			"dateCreated":              time.Now().Format("2006-01-02T00:00:00Z"),
			"programDate":              time.Now().Format("2006-01-02T00:00:00Z"),
			"programRevisionDate":      time.Now().Format("2006-01-02T00:00:00Z"),
			"reporterName":             "REALMASTER",                                           // fixed value
			"reporterAddress":          "50 Acadia Ave #130, Markham, Ontario, Canada L3R 5Z2", // fixed value
			"reporterTelephoneNumber":  6475185728,                                             // fixed value
		},
		"data": []map[string]interface{}{},
	}

	// Process each lease
	for i, lease := range leases {
		safeMetro2Log("trace", "Processing lease for Metro2",
			"leaseIndex", i+1,
			"leaseID", lease.ID,
			"propertyID", lease.PropertyID,
			"startDate", lease.StartDate,
			"endDate", lease.EndDate,
			"rentAmount", lease.RentAmount,
			"tenantCount", len(lease.CurrentTenants))

		// Skip lease with no tenants
		if len(lease.CurrentTenants) == 0 {
			safeMetro2Log("debug", "Skipping lease: no tenants", "leaseID", lease.ID)
			continue
		}

		var property *Property
		if user.Role == RoleAdmin {
			// admin can query all properties - use admin function without access control
			property, err = GetPropertyByIDAdmin(ctx, lease.PropertyID)
		} else {
			property, err = GetProperty(ctx, lease.PropertyID, userID)
		}
		if err != nil {
			safeMetro2Log("debug", "Skipping lease: failed to get property info", "leaseID", lease.ID, "error", err)
			continue
		}

		safeMetro2Log("trace", "Property info for Metro2",
			"leaseID", lease.ID,
			"street", property.Address.Street,
			"unit", property.Address.Unit,
			"city", property.Address.City,
			"province", property.Address.Prov,
			"zipCode", property.Address.ZipCode)

		payments, _, err := GetPaymentsUpToDate(ctx, lease.ID, userID, endOfMonth)
		if err != nil {
			safeMetro2Log("debug", "Skipping lease: failed to get payment info", "leaseID", lease.ID, "error", err)
			continue
		}

		safeMetro2Log("trace", "Payment records for Metro2", "leaseID", lease.ID, "paymentCount", len(payments))

		// Collect tenant names for logging
		tenantNames := make([]string, 0, len(lease.CurrentTenants))
		for _, tenant := range lease.CurrentTenants {
			tenantNames = append(tenantNames, fmt.Sprintf("%s %s", tenant.FirstName, tenant.LastName))
		}

		// Create lease process info for logging
		leaseInfo := LeaseProcessInfo{
			LeaseID:         lease.ID,
			PropertyName:    property.Name,
			PropertyAddress: fmt.Sprintf("%s %s, %s, %s %s", property.Address.Street, property.Address.Unit, property.Address.City, property.Address.Prov, property.Address.ZipCode),
			TenantCount:     len(lease.CurrentTenants),
			TenantNames:     tenantNames,
			PaymentCount:    len(payments),
			RentAmount:      lease.RentAmount,
			CurrentBalance:  calculateCurrentBalance(lease, payments),
			AccountStatus:   calculateAccountStatue(lease, payments, endOfMonth),
			StartDate:       lease.StartDate,
			EndDate:         lease.EndDate,
		}

		// Add to generation log
		generationLog.ProcessedLeases = append(generationLog.ProcessedLeases, leaseInfo)
		generationLog.TotalTenants += len(lease.CurrentTenants)
		generationLog.TotalPayments += len(payments)

		primaryTenant := lease.CurrentTenants[0]
		safeMetro2Log("trace", "Primary tenant info for Metro2",
			"leaseID", lease.ID,
			"tenantID", primaryTenant.ID,
			"firstName", primaryTenant.FirstName,
			"lastName", primaryTenant.LastName,
			"sinNumber", primaryTenant.SINNumber,
			"phone", primaryTenant.Phone)

		// Show joint tenants info
		if len(lease.CurrentTenants) > 1 {
			safeMetro2Log("trace", "Joint tenants info for Metro2",
				"leaseID", lease.ID,
				"jointTenantCount", len(lease.CurrentTenants)-1)
		}

		baseData := map[string]interface{}{
			"recordDescriptorWord":          calculateRDW(leases),
			"processingIndicator":           1, // fixed value
			"timeStamp":                     time.Now().Format("2006-01-02T15:04:05Z"),
			"identificationNumber":          "467RE01193", // fixed value
			"consumerAccountNumber":         strings.TrimSpace(fmt.Sprintf("TN%s", primaryTenant.ID)),
			"portfolioType":                 "O",  // fixed value, Open Account (rent)
			"accountType":                   "29", // fixed value, rental account
			"dateOpened":                    parseDate(lease.StartDate, endOfMonth),
			"highestCredit":                 int(lease.RentAmount),
			"termsDuration":                 "001", // fixed value, 001
			"scheduledMonthlyPaymentAmount": int(lease.RentAmount),
			"actualPaymentAmount":           getActualPaymentAmount(payments, endOfMonth),
			"accountStatus":                 calculateAccountStatue(lease, payments, endOfMonth),
			"paymentHistoryProfile":         calculatePaymentHistory(payments, lease, endOfMonth),
			"specialComment":                "",
			"currentBalance":                calculateCurrentBalance(lease, payments),
			"dateAccountInformation":        endOfMonth.Format("2006-01-02T00:00:00Z"),
			"surname":                       strings.TrimSpace(primaryTenant.LastName),
			"firstName":                     strings.TrimSpace(primaryTenant.FirstName),
			"middleName":                    strings.TrimSpace(primaryTenant.MiddleName),
			"socialSecurityNumber":          parseSIN(primaryTenant.SINNumber),
			"dateBirth":                     formatDateOfBirth(primaryTenant.DateOfBirth),
			"telephoneNumber":               parsePhoneNumber(primaryTenant.Phone),
			"ecoaCode":                      getEcoaCode(len(lease.CurrentTenants)),
			"countryCode":                   "CA", // fixed value
			"firstLineAddress":              strings.TrimSpace(property.Address.Street),
			"secondLineAddress":             strings.TrimSpace(property.Address.Unit),
			"city":                          strings.TrimSpace(property.Address.City),
			"state":                         strings.TrimSpace(property.Address.Prov),
			"zipCode":                       strings.TrimSpace(property.Address.ZipCode),
			"addressIndicator":              "Y", // fixed value
		}

		// Only add amountPastDue if account status indicates delinquency
		accountStatus := calculateAccountStatue(lease, payments, endOfMonth)
		if accountStatus != "11" && accountStatus != "13" {
			amountPastDue := calculateAmountPastDue(lease, payments, endOfMonth)
			if amountPastDue > 0 {
				baseData["amountPastDue"] = amountPastDue
			}
		}
		// Only fill in dateFirstDelinquency if account status indicates delinquency
		if accountStatus != "11" && accountStatus != "13" && getDateFirstDelinquency(lease, payments, endOfMonth) != "" {
			baseData["dateFirstDelinquency"] = getDateFirstDelinquency(lease, payments, endOfMonth)
		}
		// Only fill in if there are payments
		if getLastPaymentDate(payments, endOfMonth) != "" {
			baseData["dateLastPayment"] = getLastPaymentDate(payments, endOfMonth)
		}
		// Add dateClosed if lease has ended
		if lease.EndDate != "" {
			if leaseEndDate, err := time.Parse("2006-01-02", lease.EndDate); err == nil {
				if endOfMonth.After(leaseEndDate) || endOfMonth.Equal(leaseEndDate) {
					baseData["dateClosed"] = leaseEndDate.Format("2006-01-02T00:00:00Z")
				}
			}
		}
		// J1 segment processing
		var baseSegment map[string]interface{}
		if len(lease.CurrentTenants) > 1 {
			j1Segments := make([]map[string]interface{}, 0)
			for _, tenant := range lease.CurrentTenants[1:] {
				j1Segment := map[string]interface{}{
					"segmentIdentifier":    "J1", // 固定值
					"surname":              strings.TrimSpace(tenant.LastName),
					"firstName":            strings.TrimSpace(tenant.FirstName),
					"generationCode":       "", // 固定为空
					"socialSecurityNumber": parseSIN(tenant.SINNumber),
					"dateBirth":            formatDateOfBirth(tenant.DateOfBirth),
					"telephoneNumber":      parsePhoneNumber(tenant.Phone),
					"ecoaCode":             "2", // 固定值
				}
				j1Segments = append(j1Segments, j1Segment)
			}
			baseSegment = map[string]interface{}{
				"base": baseData,
				"j1":   j1Segments,
			}
		} else {
			baseSegment = map[string]interface{}{
				"base": baseData,
			}
		}
		metro2Data["data"] = append(metro2Data["data"].([]map[string]interface{}), baseSegment)
	}

	safeMetro2Log("info", "Metro2 data generation completed", "finalRecordCount", len(metro2Data["data"].([]map[string]interface{})))

	// Generate JSON data
	jsonData, err := json.Marshal(metro2Data)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to marshal metro2 data: %v", err)
	}

	// Update generation log with final statistics
	generationLog.JSONDataSize = int64(len(jsonData))

	safeMetro2Log("info", "Metro2 generation log summary",
		"totalLeases", generationLog.TotalLeases,
		"processedLeases", len(generationLog.ProcessedLeases),
		"totalTenants", generationLog.TotalTenants,
		"totalPayments", generationLog.TotalPayments,
		"jsonDataSize", generationLog.JSONDataSize)

	return jsonData, generationLog, nil
}

// GetLeasesForMonth returns all leases that were active during the given month
func GetLeasesForMonth(ctx context.Context, reportDate time.Time, userID string) ([]*Lease, error) {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	// 获取用户信息，判断是否admin
	user, err := GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %v", err)
	}

	// Calculate start and end of the month
	startOfMonth := time.Date(reportDate.Year(), reportDate.Month(), 1, 0, 0, 0, 0, reportDate.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Second)

	// Format dates in YYYY-MM-DD format for MongoDB
	startDateStr := startOfMonth.Format("2006-01-02")
	endDateStr := endOfMonth.Format("2006-01-02")

	// Find leases that were active during the month
	filter := bson.M{
		"status":  "active",
		"rentRep": true,
		"$or": []bson.M{
			{
				"startDt": bson.M{"$lte": endDateStr},
				"endDt":   bson.M{"$gte": startDateStr},
			},
			{
				"startDt": bson.M{"$lte": endDateStr},
				"endDt":   "",
			},
		},
	}

	// 不是admin只能查自己的lease
	if user.Role != RoleAdmin {
		filter["usrId"] = userID
	}

	cursor, err := leaseColl.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find leases: %v", err)
	}
	defer cursor.Close(ctx)

	leases := make([]*Lease, 0)
	if err = cursor.All(ctx, &leases); err != nil {
		return nil, fmt.Errorf("failed to decode leases: %v", err)
	}

	return leases, nil
}

// Helper functions
func calculateRDW(leases []*Lease) int {
	// TODO: calculate RDW based on lease
	// 426 + 100 * maximum_tenant_number_of_all_reporting_leases
	// return DefaultMetro2Constants.RecordDescriptorWord
	maxTenants := 1
	for _, lease := range leases {
		if len(lease.CurrentTenants) > maxTenants {
			maxTenants = len(lease.CurrentTenants)
		}
	}
	return 426 + 100*(maxTenants-1)
}

func calculatePaymentHistory(payments []*TenantPayment, lease *Lease, reportMonth time.Time) string {
	// TODO: calculate histroy based on past due
	//0 = Current (paid as agreed)
	//1 = 30-59 days past due
	//2 = 60-89 days past due
	//3 = 90-119 days past due
	//4 = 120-149 days past due
	//5 = 150-179 days past due
	//6 = 180 or more days past due date
	//B = No payment history available prior to this time – either because the account was not open or because the payment history cannot be furnished. A "B" may not be embedded within other values.
	//D = No payment history reported/available this month. "D" may be embedded in the payment history profile.
	//E = Zero balance and current account (Applies to Credit Cards and Lines of Credit)
	//G = Collection
	//H = Foreclosure Completed
	//J = Voluntary Surrender
	//K = Repossession
	//L = Charge-off

	// 基于简化逻辑重新设计24个月付款历史
	history := make([]string, 24)
	// 使用报告月份而不是当前时间
	reportDate := reportMonth

	// 解析租约开始和结束日期
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return "BBBBBBBBBBBBBBBBBBBBBBBB"
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			return "BBBBBBBBBBBBBBBBBBBBBBBB"
		}
	}

	// 逐月检查付款历史（从报告月份到24个月前）
	// Metro2 标准：从左到右表示最新 → 最旧
	for i := 0; i < 24; i++ {
		month := reportDate.AddDate(0, -i, 0)

		// 如果月份在租约开始前或结束后，标记为 B
		if month.Before(leaseStart) || (!leaseEnd.IsZero() && month.After(leaseEnd)) {
			history[i] = "B"
			continue
		}

		// 计算该月的总付款金额
		monthlyPayment := 0.0
		for _, payment := range payments {
			if payment.Date.Year() == month.Year() && payment.Date.Month() == month.Month() {
				monthlyPayment += payment.Amount
			}
		}

		// 简化逻辑：基于该月付款与租金的比较
		monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

		if monthlyPayment >= monthlyRent {
			// 足额付款，标记为当前
			history[i] = "0"
		} else if monthlyPayment > 0 {
			// 有付款但不足额，标记为30天逾期
			history[i] = "1"
		} else {
			// 无付款，根据月份距离报告时间判断逾期程度
			monthsFromReport := int(reportDate.Sub(month).Hours() / 24 / 30)
			switch {
			case monthsFromReport <= 1:
				history[i] = "1" // 30-59天逾期
			case monthsFromReport <= 2:
				history[i] = "2" // 60-89天逾期
			case monthsFromReport <= 3:
				history[i] = "3" // 90-119天逾期
			case monthsFromReport <= 4:
				history[i] = "4" // 120-149天逾期
			case monthsFromReport <= 5:
				history[i] = "5" // 150-179天逾期
			default:
				history[i] = "6" // 180+天逾期
			}
		}
	}

	return strings.Join(history, "")
}

func calculateAccountStatue(lease *Lease, payments []*TenantPayment, reportMonth time.Time) string {
	// TODO: calculate status based on past due
	//  11 = Current account, to be used if the account is not past due and for current accounts
	//  13 = Paid or closed account/zero balance
	//  71 = Account 30-59 days past due
	//  78 = Account 60-89 days past due
	//  80 = Account 90-119 days past due
	//  82 = Account 120-149 days past due
	//  83 = Account 150-179 days past due
	//  84 = Account 180+ days past due
	// 计算当前逾期天数，与calculatePaymentHistory逻辑保持一致
	//return 11
	overdueDays := 0

	// 按时间升序排列payments
	sort.Slice(payments, func(i, j int) bool {
		return payments[i].Date.Before(payments[j].Date)
	})

	leaseStart, _ := time.Parse("2006-01-02", lease.StartDate)
	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, _ = time.Parse("2006-01-02", lease.EndDate)
	}

	// 检查租约是否已结束
	if !leaseEnd.IsZero() && reportMonth.After(leaseEnd) {
		//fmt.Printf("[INFO] account status: 13 (lease ended)\n")
		return "13" // Paid or closed account/zero balance
	}

	// 检查租约是否还未开始
	if reportMonth.Before(leaseStart) {
		//fmt.Printf("[INFO] account status: 11 (lease not started)\n")
		return "11" // Current account
	}

	// 计算当前逾期天数
	currentMonth := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())
	totalPaid := 0.0
	minRemBal := -1.0

	for _, p := range payments {
		if p.Date.Year() == currentMonth.Year() && p.Date.Month() == currentMonth.Month() {
			totalPaid += p.Amount
			if minRemBal < 0 || p.RemainingBalance < minRemBal {
				minRemBal = p.RemainingBalance
			}
		}
	}

	// 如果本月足额还款或余额为0，则当前状态
	if (totalPaid >= lease.RentAmount) || (minRemBal == 0) {
		//fmt.Printf("[INFO] account status: 11 (current month paid in full)\n")
		return "11" // Current account
	}

	// 计算累计逾期天数
	// 从报告月开始向前检查（与 payment history 保持一致）
	for i := 0; i < 24; i++ {
		month := reportMonth.AddDate(0, -i, 0)
		if month.Before(leaseStart) || (!leaseEnd.IsZero() && month.After(leaseEnd)) {
			continue
		}

		monthTotalPaid := 0.0
		monthMinRemBal := -1.0
		for _, p := range payments {
			if p.Date.Year() == month.Year() && p.Date.Month() == month.Month() {
				monthTotalPaid += p.Amount
				if monthMinRemBal < 0 || p.RemainingBalance < monthMinRemBal {
					monthMinRemBal = p.RemainingBalance
				}
			}
		}

		if (monthTotalPaid >= lease.RentAmount) || (monthMinRemBal == 0) {
			overdueDays = 0
		} else {
			overdueDays += 30
		}
	}

	//fmt.Printf("[INFO] account status overdue days: %v\n", overdueDays)

	// 根据逾期天数返回状态码
	switch {
	case overdueDays < 30:
		return "11" // Current account
	case overdueDays < 60:
		return "71" // Account 30-59 days past due
	case overdueDays < 90:
		return "78" // Account 60-89 days past due
	case overdueDays < 120:
		return "80" // Account 90-119 days past due
	case overdueDays < 150:
		return "82" // Account 120-149 days past due
	case overdueDays < 180:
		return "83" // Account 150-179 days past due
	default:
		return "84" // Account 180+ days past due
	}
}

func calculateCurrentBalance(lease *Lease, payments []*TenantPayment) float64 {
	// 直接使用租赁的 owingBalance 字段
	// Metro2 规则：负数余额显示为 0（表示多付款）
	if lease.OwingBalance < 0 {
		return 0.0
	}

	return lease.OwingBalance
}

func calculateAmountPastDue(lease *Lease, payments []*TenantPayment, reportMonth time.Time) float64 {
	// 基于 owingBalance 简化逻辑
	// 如果 owingBalance <= 0，没有逾期
	if lease.OwingBalance <= 0 {
		return 0
	}

	// 检查账户状态，如果当前账户则无逾期
	accountStatus := calculateAccountStatue(lease, payments, reportMonth)
	if accountStatus == "11" {
		return 0 // 当前账户，无逾期
	}

	// 检查租约状态
	leaseStart, _ := time.Parse("2006-01-02", lease.StartDate)
	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, _ = time.Parse("2006-01-02", lease.EndDate)
	}

	// 如果租约已结束或未开始，无逾期
	if (!leaseEnd.IsZero() && reportMonth.After(leaseEnd)) || reportMonth.Before(leaseStart) {
		return 0
	}

	// 简化逻辑：如果账户不是当前状态且有欠款，则所有欠款都视为逾期
	// 这里可以根据业务需求进一步细化，比如只计算超过当月的部分
	return lease.OwingBalance
}

// parseDate converts a date string to the format expected by Metro2
// maxDate参数确保返回的日期不超过指定的最大日期
func parseDate(dateStr string, maxDate time.Time) string {
	if dateStr == "" {
		// 对于必填日期字段，返回报告月份的最后一天；对于可选字段，调用方应该处理
		return maxDate.Format("2006-01-02T00:00:00Z")
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		// If parsing fails, return report month end date for required fields
		return maxDate.Format("2006-01-02T00:00:00Z")
	}

	// 如果解析的日期超过了最大日期，返回最大日期
	if date.After(maxDate) {
		return maxDate.Format("2006-01-02T00:00:00Z")
	}

	return date.Format("2006-01-02T00:00:00Z")
}

// getLastPaymentDate returns the date of the most recent payment in Metro2 format
// maxDate参数确保返回的日期不超过指定的最大日期
func getLastPaymentDate(payments []*TenantPayment, maxDate time.Time) string {
	if len(payments) == 0 {
		// 如果没有付款记录，返回空字符串，但在使用时需要检查
		return ""
	}

	// Find the most recent payment date that doesn't exceed maxDate
	var latestDate time.Time
	for _, payment := range payments {
		// 只考虑不超过maxDate的payment
		if payment.Date.After(latestDate) && !payment.Date.After(maxDate) {
			latestDate = payment.Date
		}
	}

	// If no valid date found, return empty string
	if latestDate.IsZero() {
		return ""
	}

	return latestDate.Format("2006-01-02T00:00:00Z")
}

func getDateFirstDelinquency(lease *Lease, payments []*TenantPayment, reportMonth time.Time) string {
	// 首先检查账户状态，只有逾期账户才需要首次逾期日期
	accountStatus := calculateAccountStatue(lease, payments, reportMonth)
	if accountStatus == "11" || accountStatus == "13" {
		// 当前账户或已关闭账户不需要首次逾期日期
		return ""
	}

	// 如果没有支付记录，从租约开始的第二个月作为首次逾期日期
	if len(payments) == 0 {
		leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
		if err != nil {
			return ""
		}
		// 租约开始后的第一个月末作为首次逾期日期
		firstMonth := leaseStart.AddDate(0, 1, 0)
		firstMonthStart := time.Date(firstMonth.Year(), firstMonth.Month(), 1, 0, 0, 0, 0, firstMonth.Location())
		return firstMonthStart.Format("2006-01-02T00:00:00Z")
	}

	// 解析租约开始和结束日期
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return ""
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			return ""
		}
	}

	// 按时间升序排列支付记录
	sort.Slice(payments, func(i, j int) bool {
		return payments[i].Date.Before(payments[j].Date)
	})

	// 从租约开始日期开始，逐月检查是否逾期
	currentDate := leaseStart

	// 如果租约还未开始，返回空字符串
	if reportMonth.Before(leaseStart) {
		return ""
	}

	// 逐月检查逾期情况（直到报告月份）
	for currentDate.Before(reportMonth) || currentDate.Equal(reportMonth) {
		// 跳过租约开始前的月份
		if currentDate.Before(leaseStart) {
			currentDate = currentDate.AddDate(0, 1, 0)
			continue
		}

		// 跳过租约结束后的月份
		if !leaseEnd.IsZero() && currentDate.After(leaseEnd) {
			break
		}

		// 计算当前月份的总支付金额
		monthTotalPaid := 0.0
		monthStart := time.Date(currentDate.Year(), currentDate.Month(), 1, 0, 0, 0, 0, currentDate.Location())
		monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Second)

		for _, payment := range payments {
			if payment.Date.After(monthStart) && payment.Date.Before(monthEnd.AddDate(0, 0, 1)) {
				monthTotalPaid += payment.Amount
			}
		}

		// 如果当月支付金额不足月租金，则认为是首次逾期
		if monthTotalPaid < lease.RentAmount {
			// 返回该月的第一天作为首次逾期日期
			return monthStart.Format("2006-01-02T00:00:00Z")
		}

		// 检查下一个月
		currentDate = currentDate.AddDate(0, 1, 0)
	}

	// 如果没有找到逾期情况，返回空字符串
	return ""
}

// formatDateOfBirth formats the date of birth in Metro2 format
func formatDateOfBirth(dob *time.Time) string {
	if dob == nil || dob.IsZero() {
		// DateBirth 是 nullable 字段，但 Metro2 库期望有效的日期格式
		// 使用一个默认的生日日期，比如 1900-01-01
		defaultDate := time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC)
		return defaultDate.Format("2006-01-02T00:00:00Z")
	}

	return dob.Format("2006-01-02T00:00:00Z")
}

// parsePhoneNumber converts a phone string to an integer by removing non-numeric characters
func parsePhoneNumber(phone string) int64 {
	// Remove all non-numeric characters and trim spaces
	phone = strings.TrimSpace(phone)
	var numericOnly string
	for _, char := range phone {
		if char >= '0' && char <= '9' {
			numericOnly += string(char)
		}
	}

	// Convert to int64, ignoring any error
	result, _ := strconv.ParseInt(numericOnly, 10, 64)
	return result
}

// parseSIN converts a SIN string to an integer by removing non-numeric characters
func parseSIN(sin string) int64 {
	// Remove all non-numeric characters and trim spaces
	sin = strings.TrimSpace(sin)
	var numericOnly string
	for _, char := range sin {
		if char >= '0' && char <= '9' {
			numericOnly += string(char)
		}
	}

	// Convert to int64, ignoring any error
	result, _ := strconv.ParseInt(numericOnly, 10, 64)
	return result
}

// getEcoaCode returns the appropriate ECOA code based on number of tenants
func getEcoaCode(numTenants int) string {
	if numTenants > 1 {
		return "2" // Joint account
	}
	return "1" // Individual account
}

// getActualPaymentAmount returns the total payment amount for the report month
func getActualPaymentAmount(payments []*TenantPayment, reportMonth time.Time) int {
	if len(payments) == 0 {
		return 0
	}

	// Calculate total payments for the report month
	totalAmount := 0.0
	for _, payment := range payments {
		if payment.Date.Year() == reportMonth.Year() && payment.Date.Month() == reportMonth.Month() {
			totalAmount += payment.Amount
		}
	}

	return int(totalAmount)
}
