package entities

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// RentReportEmailQueue 租金报告邮件队列（简化版，基于租户ID去重）
type RentReportEmailQueue struct {
	ID            string    `bson:"_id" json:"id"`
	TntId         string    `bson:"tntId" json:"tntId"`                 // 租户ID（唯一标识）
	TntEmail      string    `bson:"tntEmail" json:"tntEmail"`           // 租户邮箱
	TntFirstName  string    `bson:"tntFirstName" json:"tntFirstName"`   // 租户名字
	LeaseId       string    `bson:"leaseId" json:"leaseId"`             // 租约ID
	PropId        string    `bson:"propId" json:"propId"`               // 属性ID
	LandlordId    string    `bson:"landlordId" json:"landlordId"`       // 房东ID
	InitialStatus string    `bson:"initialStatus" json:"initialStatus"` // 初始状态: active|inactive
	FinalStatus   string    `bson:"finalStatus" json:"finalStatus"`     // 最终状态: active|inactive
	LastUpdated   time.Time `bson:"lastUpdated" json:"lastUpdated"`     // 最后更新时间
	CreatedAt     time.Time `bson:"createdAt" json:"createdAt"`         // 创建时间
	Processed     bool      `bson:"processed" json:"processed"`         // 是否已处理
}

// UpsertRentReportEmailQueue 更新或插入租金报告邮件队列记录
func UpsertRentReportEmailQueue(ctx context.Context, tenantId, tenantEmail, tenantFirstName, leaseId, propId, landlordId, finalStatus string) error {
	coll := gomongo.Coll("rr", "rent_report_email_queue")
	if coll == nil {
		return fmt.Errorf("rent_report_email_queue collection not initialized")
	}

	now := time.Now()
	filter := bson.M{
		"tntId":     tenantId,
		"processed": false, // 只更新未处理的记录
	}

	// 检查是否已存在记录
	var existingRecord RentReportEmailQueue
	err := coll.FindOne(ctx, filter).Decode(&existingRecord)

	var update bson.M
	if err != nil {
		// 记录不存在，创建新记录，初始状态和最终状态都设为当前状态
		update = bson.M{
			"$set": bson.M{
				"tntEmail":      tenantEmail,
				"tntFirstName":  tenantFirstName,
				"leaseId":       leaseId,
				"propId":        propId,
				"landlordId":    landlordId,
				"initialStatus": finalStatus, // 新记录的初始状态
				"finalStatus":   finalStatus,
				"lastUpdated":   now,
			},
			"$setOnInsert": bson.M{
				"_id":       tenantId + "_" + fmt.Sprintf("%d", now.Unix()), // 使用租户ID+时间戳作为唯一ID
				"createdAt": now,
				"processed": false,
			},
		}
	} else {
		// 记录已存在，只更新最终状态，保持初始状态不变
		update = bson.M{
			"$set": bson.M{
				"tntEmail":     tenantEmail,
				"tntFirstName": tenantFirstName,
				"leaseId":      leaseId,
				"propId":       propId,
				"landlordId":   landlordId,
				"finalStatus":  finalStatus, // 只更新最终状态
				"lastUpdated":  now,
			},
		}
	}

	opts := options.Update().SetUpsert(true)
	_, err = coll.UpdateOne(ctx, filter, update, opts)
	return err
}

// GetUnprocessedRentReportEmailQueue 获取未处理的租金报告邮件队列记录
func GetUnprocessedRentReportEmailQueue(ctx context.Context, windowMinutes int) ([]RentReportEmailQueue, error) {
	coll := gomongo.Coll("rr", "rent_report_email_queue")
	if coll == nil {
		return nil, fmt.Errorf("rent_report_email_queue collection not initialized")
	}

	// 计算时间窗口：处理 windowMinutes 分钟前到现在的记录
	now := time.Now()
	windowStart := now.Add(-time.Duration(windowMinutes) * time.Minute)

	filter := bson.M{
		"processed":   false,
		"lastUpdated": bson.M{"$gte": windowStart, "$lte": now},
	}

	cursor, err := coll.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []RentReportEmailQueue
	if err = cursor.All(ctx, &records); err != nil {
		return nil, err
	}

	return records, nil
}

// MarkRentReportEmailQueueProcessed 标记租金报告邮件队列记录为已处理
func MarkRentReportEmailQueueProcessed(ctx context.Context, id string) error {
	coll := gomongo.Coll("rr", "rent_report_email_queue")
	if coll == nil {
		return fmt.Errorf("rent_report_email_queue collection not initialized")
	}

	filter := bson.M{"_id": id}
	update := bson.M{"$set": bson.M{
		"processed":   true,
		"lastUpdated": time.Now(),
	}}

	_, err := coll.UpdateOne(ctx, filter, update)
	return err
}

// CleanupOldRentReportEmailQueue 清理旧的租金报告邮件队列记录
func CleanupOldRentReportEmailQueue(ctx context.Context, daysOld int) error {
	coll := gomongo.Coll("rr", "rent_report_email_queue")
	if coll == nil {
		return fmt.Errorf("rent_report_email_queue collection not initialized")
	}

	cutoffTime := time.Now().AddDate(0, 0, -daysOld)
	filter := bson.M{
		"processed": true,
		"createdAt": bson.M{"$lt": cutoffTime},
	}

	_, err := coll.DeleteMany(ctx, filter)
	return err
}
