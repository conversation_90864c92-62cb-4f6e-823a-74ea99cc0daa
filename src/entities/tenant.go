package entities

import (
	"context"
	"fmt"
	"rent_report/utils"
	"rent_report/utils/encryption"
	"strconv"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// getDelayMinutesFromConfig 从配置文件获取延迟时间，默认3分钟
func getDelayMinutesFromConfig() int {
	delayMinutes := 3 // 统一默认值：3分钟
	if delayConfig := goconfig.Config("delayedEmailNotification.delayMinutes"); delayConfig != nil {
		// 尝试多种类型转换
		switch v := delayConfig.(type) {
		case int:
			delayMinutes = v
		case int64:
			delayMinutes = int(v)
		case float64:
			delayMinutes = int(v)
		case string:
			if parsed, err := strconv.Atoi(v); err == nil {
				delayMinutes = parsed
			}
		}
	}
	return delayMinutes
}

type Tenant struct {
	ID             string     `json:"id" bson:"_id"`
	LeaseID        string     `json:"leaseId" bson:"leaseId"`
	UserID         string     `json:"-" bson:"usrId"`
	TenantId       string     `json:"tenantId" bson:"tenantId"`
	FirstName      string     `json:"firstName" bson:"firstNm"`
	MiddleName     string     `json:"middleName" bson:"midNm"`
	LastName       string     `json:"lastName" bson:"lastNm"`
	Email          string     `json:"email" bson:"email"`
	Phone          string     `json:"phoneNumber" bson:"-"`           // Exclude from direct BSON
	encryptedPhone string     `json:"-" bson:"phone"`                 // Store encrypted value
	SINNumber      string     `json:"sinNumber" bson:"-"`             // Exclude from direct BSON, handle separately
	encryptedSIN   string     `json:"-" bson:"sin"`                   // Store encrypted value
	DateOfBirth    *time.Time `json:"dateOfBirth,omitempty" bson:"-"` // Exclude from direct BSON
	encryptedDOB   string     `json:"-" bson:"dob"`                   // Store encrypted value
	Notes          string     `json:"notes" bson:"notes"`
	OrganizationID string     `json:"-" bson:"orgId,omitempty"`
	IsProtected    bool       `json:"isProtected" bson:"isProtected"` // 是否受保护，不可删除（如用于Metro2报告）
	TenantType     string     `json:"tenantType,omitempty" bson:"-"`  // 不存储在数据库中，仅用于创建时指定类型
	// Additional fields for search results (not stored in DB)
	PropertyName string `json:"propertyName,omitempty" bson:"-"`
	RoomName     string `json:"roomName,omitempty" bson:"-"`
	LeaseStatus  string `json:"leaseStatus,omitempty" bson:"-"`
}

// MarshalBSON implements the bson.Marshaler interface
func (tenant *Tenant) MarshalBSON() ([]byte, error) {
	golog.Debug("MarshalBSON called for tenant", "tenantId", tenant.ID)

	// Create a map for BSON document
	doc := bson.M{
		"_id":         tenant.ID,
		"leaseId":     tenant.LeaseID,
		"usrId":       tenant.UserID,
		"tenantId":    tenant.TenantId,
		"firstNm":     tenant.FirstName,
		"midNm":       tenant.MiddleName,
		"lastNm":      tenant.LastName,
		"email":       tenant.Email,
		"notes":       tenant.Notes,
		"orgId":       tenant.OrganizationID,
		"isProtected": tenant.IsProtected,
	}

	// Encrypt and add sensitive fields
	if tenant.SINNumber != "" {
		golog.Debug("Encrypting SIN for tenant", "tenantId", tenant.ID)
		encrypted, err := encryption.EncryptString(tenant.SINNumber)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt SIN: %v", err)
		}
		doc["sin"] = encrypted
		golog.Debug("SIN encrypted successfully for tenant", "tenantId", tenant.ID)
	}

	if tenant.DateOfBirth != nil {
		golog.Debug("Encrypting DOB for tenant", "tenantId", tenant.ID)
		dobStr := tenant.DateOfBirth.Format(time.RFC3339)
		encrypted, err := encryption.EncryptString(dobStr)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt DOB: %v", err)
		}
		doc["dob"] = encrypted
		golog.Debug("DOB encrypted successfully for tenant", "tenantId", tenant.ID)
	}

	if tenant.Phone != "" {
		golog.Debug("Encrypting Phone for tenant", "tenantId", tenant.ID)
		encrypted, err := encryption.EncryptString(tenant.Phone)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt phone: %v", err)
		}
		doc["phone"] = encrypted
		golog.Debug("Phone encrypted successfully for tenant", "tenantId", tenant.ID)
	}

	return bson.Marshal(doc)
}

// UnmarshalBSON implements the bson.Unmarshaler interface
func (tenant *Tenant) UnmarshalBSON(data []byte) error {
	// Create a temporary structure to hold the BSON data
	var doc struct {
		ID             string `bson:"_id"`
		LeaseID        string `bson:"leaseId"`
		UserID         string `bson:"usrId"`
		TenantId       string `bson:"tenantId"`
		FirstName      string `bson:"firstNm"`
		MiddleName     string `bson:"midNm"`
		LastName       string `bson:"lastNm"`
		Email          string `bson:"email"`
		EncryptedPhone string `bson:"phone,omitempty"`
		EncryptedSIN   string `bson:"sin,omitempty"`
		EncryptedDOB   string `bson:"dob,omitempty"`
		Notes          string `bson:"notes"`
		OrganizationID string `bson:"orgId,omitempty"`
		IsProtected    bool   `bson:"isProtected"`
	}

	if err := bson.Unmarshal(data, &doc); err != nil {
		return fmt.Errorf("failed to unmarshal tenant data: %v", err)
	}

	// Copy the basic fields
	tenant.ID = doc.ID
	tenant.LeaseID = doc.LeaseID
	tenant.UserID = doc.UserID
	tenant.TenantId = doc.TenantId
	tenant.FirstName = doc.FirstName
	tenant.MiddleName = doc.MiddleName
	tenant.LastName = doc.LastName
	tenant.Email = doc.Email
	tenant.Notes = doc.Notes
	tenant.OrganizationID = doc.OrganizationID
	tenant.IsProtected = doc.IsProtected

	golog.Debug("UnmarshalBSON called for tenant", "tenantId", tenant.ID)

	// Decrypt sensitive fields
	if doc.EncryptedSIN != "" {
		golog.Debug("Decrypting SIN for tenant", "tenantId", tenant.ID)
		decrypted, err := encryption.DecryptString(doc.EncryptedSIN)
		if err != nil {
			return fmt.Errorf("failed to decrypt SIN: %v", err)
		}
		tenant.SINNumber = decrypted
		golog.Debug("SIN decrypted successfully for tenant", "tenantId", tenant.ID)
	}

	if doc.EncryptedDOB != "" {
		golog.Debug("Decrypting DOB for tenant", "tenantId", tenant.ID)
		decrypted, err := encryption.DecryptString(doc.EncryptedDOB)
		if err != nil {
			return fmt.Errorf("failed to decrypt DOB: %v", err)
		}
		parsedTime, err := time.Parse(time.RFC3339, decrypted)
		if err != nil {
			return fmt.Errorf("failed to parse decrypted DOB: %v", err)
		}
		tenant.DateOfBirth = &parsedTime
		golog.Debug("DOB decrypted successfully for tenant", "tenantId", tenant.ID)
	}

	if doc.EncryptedPhone != "" {
		golog.Debug("Decrypting Phone for tenant", "tenantId", tenant.ID)
		decrypted, err := encryption.DecryptString(doc.EncryptedPhone)
		if err != nil {
			return fmt.Errorf("failed to decrypt phone: %v", err)
		}
		tenant.Phone = decrypted
		golog.Debug("Phone decrypted successfully for tenant", "tenantId", tenant.ID)
	}

	return nil
}

func (tenant *Tenant) Create(ctx context.Context) error {
	golog.Info("Tenant.Create called",
		"tenantId", tenant.ID,
		"leaseId", tenant.LeaseID,
		"tenantEmail", tenant.Email,
		"tenantType", tenant.TenantType,
		"userId", tenant.UserID)

	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return fmt.Errorf("tenants collection not initialized")
	}

	scope, err := GetResourceScope(ctx, tenant.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	// Set organization based on scope
	tenant.OrganizationID = scope.OrganizationID

	// Validate required fields
	if tenant.ID == "" || tenant.LeaseID == "" || tenant.FirstName == "" || tenant.LastName == "" || tenant.Email == "" {
		return fmt.Errorf("id, leaseId, firstName, lastName and email are required")
	}

	// Verify lease exists and update its tenantId
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	leaseFilter := bson.M{
		"_id":   tenant.LeaseID,
		"usrId": tenant.UserID,
	}

	// 确定tenant类型，默认为current
	tenantType := tenant.TenantType
	if tenantType == "" {
		tenantType = "current"
	}

	// 初始化相应的数组
	var leaseDoc struct {
		CurrentTenants []Tenant `bson:"ctnts"`
		PastTenants    []Tenant `bson:"ptnts"`
	}
	if err := leaseColl.FindOne(ctx, leaseFilter).Decode(&leaseDoc); err == nil {
		if tenantType == "current" && leaseDoc.CurrentTenants == nil {
			_, _ = leaseColl.UpdateOne(ctx, leaseFilter, bson.M{"$set": bson.M{"ctnts": []Tenant{}}})
		} else if tenantType == "past" && leaseDoc.PastTenants == nil {
			_, _ = leaseColl.UpdateOne(ctx, leaseFilter, bson.M{"$set": bson.M{"ptnts": []Tenant{}}})
		}
	}

	// First verify lease exists and check if it already has a tenant
	var lease struct {
		TenantID string `bson:"tenantId"`
	}
	err = leaseColl.FindOne(ctx, leaseFilter).Decode(&lease)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return fmt.Errorf("lease not found")
		}
		return fmt.Errorf("failed to verify lease: %v", err)
	}

	// Insert the tenant first
	_, err = tenantColl.InsertOne(ctx, tenant)
	if err != nil {
		return fmt.Errorf("failed to insert tenant: %v", err)
	}

	// 根据tenant类型更新相应的数组
	var leaseUpdate bson.M
	if tenantType == "current" {
		leaseUpdate = bson.M{
			"$set": bson.M{},
			"$push": bson.M{
				"ctnts": tenant,
			},
		}
		// 如果是第一个当前租客，同时更新 tenantId
		if lease.TenantID == "" {
			leaseUpdate["$set"].(bson.M)["tenantId"] = tenant.ID
		}
	} else {
		// past tenant
		leaseUpdate = bson.M{
			"$set": bson.M{},
			"$push": bson.M{
				"ptnts": tenant,
			},
		}
	}

	_, err = leaseColl.UpdateOne(ctx, leaseFilter, leaseUpdate)
	if err != nil {
		// If lease update fails, try to rollback tenant creation
		_ = DeleteTenant(ctx, tenant.ID, tenant.LeaseID, tenant.UserID)
		return fmt.Errorf("failed to update lease with tenant: %v", err)
	}

	// 只对current tenant检查租约的 rent reporting 状态并记录状态变更
	if tenantType == "current" {
		golog.Info("Processing current tenant - checking rent reporting notification",
			"tenantId", tenant.ID,
			"leaseId", tenant.LeaseID,
			"tenantEmail", tenant.Email,
			"tenantType", tenantType)

		err = handleNewTenantRentReportingNotificationDelayed(ctx, tenant)
		if err != nil {
			// 记录错误但不影响主要操作
			golog.Error("Failed to handle delayed rent reporting notification for new tenant", "error", err, "tenantId", tenant.ID, "leaseId", tenant.LeaseID)
		} else {
			golog.Info("Successfully processed delayed rent reporting notification for new tenant",
				"tenantId", tenant.ID,
				"leaseId", tenant.LeaseID,
				"tenantEmail", tenant.Email)
		}
	} else {
		golog.Info("Skipping rent reporting notification - not a current tenant",
			"tenantId", tenant.ID,
			"leaseId", tenant.LeaseID,
			"tenantType", tenantType)
	}

	golog.Info("Tenant.Create completed successfully",
		"tenantId", tenant.ID,
		"leaseId", tenant.LeaseID,
		"tenantEmail", tenant.Email,
		"tenantType", tenantType)

	return nil
}

func GetTenant(ctx context.Context, id string, leaseID string, userID string) (*Tenant, error) {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return nil, fmt.Errorf("tenants collection not initialized")
	}

	filter := bson.M{
		"_id":     id,
		"leaseId": leaseID,
		"usrId":   userID,
	}

	result := tenantColl.FindOne(ctx, filter)
	if result.Err() != nil {
		if result.Err() == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("tenant not found")
		}
		return nil, fmt.Errorf("failed to get tenant: %v", result.Err())
	}

	var tenant Tenant
	if err := result.Decode(&tenant); err != nil {
		return nil, fmt.Errorf("failed to decode tenant: %v", err)
	}

	return &tenant, nil
}

func (tenant *Tenant) Update(ctx context.Context) error {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return fmt.Errorf("tenants collection not initialized")
	}

	// Validate required fields
	if tenant.ID == "" || tenant.LeaseID == "" || tenant.UserID == "" || tenant.FirstName == "" || tenant.LastName == "" || tenant.Email == "" {
		return fmt.Errorf("id, leaseId, userId, firstName, lastName and email are required")
	}

	filter := bson.M{
		"_id":     tenant.ID,
		"leaseId": tenant.LeaseID,
		"usrId":   tenant.UserID,
	}

	updateDoc, err := gomongo.ToBSONDoc(tenant)
	if err != nil {
		return fmt.Errorf("failed to create update document: %v", err)
	}

	result, err := tenantColl.UpdateOne(ctx, filter, bson.M{"$set": updateDoc})
	if err != nil {
		return fmt.Errorf("failed to update tenant: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("tenant not found")
	}

	return nil
}

func DeleteTenant(ctx context.Context, id string, leaseID string, userID string) error {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return fmt.Errorf("tenants collection not initialized")
	}

	// First check if tenant is protected
	var tenant Tenant
	err := tenantColl.FindOne(ctx, bson.M{
		"_id":     id,
		"leaseId": leaseID,
		"usrId":   userID,
	}).Decode(&tenant)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return fmt.Errorf("tenant not found")
		}
		return fmt.Errorf("failed to get tenant: %v", err)
	}

	if tenant.IsProtected {
		return fmt.Errorf("cannot delete protected tenant. This tenant is protected due to Metro2 reporting requirements")
	}

	filter := bson.M{
		"_id":     id,
		"leaseId": leaseID,
		"usrId":   userID,
	}
	result, err := tenantColl.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete tenant: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("tenant not found")
	}

	return nil
}

func GetTenants(ctx context.Context, leaseID string, userID string) ([]Tenant, int64, error) {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return nil, 0, fmt.Errorf("tenants collection not initialized")
	}

	// 获取用户的资源访问范围
	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get resource scope: %v", err)
	}

	// 构建安全的查询过滤器，包含权限验证
	filter := scope.ToFilter()
	filter["leaseId"] = leaseID

	// Get total count with security filter
	total, err := tenantColl.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	// Initialize tenants as empty slice instead of nil
	tenants := make([]Tenant, 0)

	result, err := tenantColl.FindToArray(ctx, filter, gomongo.QueryOptions{
		Sort: bson.D{{Key: "firstNm", Value: 1}},
	})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tenants with security filter: %v", err)
	}

	for _, item := range result {
		var tenant Tenant
		data, err := bson.Marshal(item)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to marshal tenant data: %v", err)
		}

		if err := bson.Unmarshal(data, &tenant); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal tenant data: %v", err)
		}

		tenants = append(tenants, tenant)
	}

	return tenants, total, nil
}

func SearchTenants(ctx context.Context, query string, userID string) ([]Tenant, int64, error) {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return nil, 0, fmt.Errorf("tenants collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get resource scope: %v", err)
	}

	// Create a case-insensitive regex pattern for the search query
	pattern := fmt.Sprintf("(?i)%s", query)
	filter := scope.ToFilter()
	filter["$or"] = []bson.M{
		{"firstNm": bson.M{"$regex": pattern}},
		{"lastNm": bson.M{"$regex": pattern}},
		{"email": bson.M{"$regex": pattern}},
	}

	// Get total count
	total, err := tenantColl.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	// Initialize tenants as empty slice instead of nil
	tenants := make([]Tenant, 0)

	result, err := tenantColl.FindToArray(ctx, filter, gomongo.QueryOptions{
		Sort:  bson.D{{Key: "firstNm", Value: 1}},
		Limit: 10, // Limit results to 10 for better performance
	})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tenants: %v", err)
	}

	// Get lease and property collections
	leaseColl := gomongo.Coll("rr", "leases")
	propColl := gomongo.Coll("rr", "properties")

	for _, item := range result {
		var tenant Tenant
		data, err := bson.Marshal(item)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to marshal tenant data: %v", err)
		}

		if err := bson.Unmarshal(data, &tenant); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal tenant data: %v", err)
		}

		// Get lease information
		if leaseColl != nil && tenant.LeaseID != "" {
			var lease struct {
				ID       string `bson:"_id"`
				Status   string `bson:"status"`
				PropID   string `bson:"propId"`
				RoomName string `bson:"roomName"`
			}

			err := leaseColl.FindOne(ctx, bson.M{"_id": tenant.LeaseID}).Decode(&lease)
			if err == nil {
				tenant.LeaseStatus = lease.Status
				tenant.RoomName = lease.RoomName

				// Get property information
				if propColl != nil && lease.PropID != "" {
					var property struct {
						Name string `bson:"nm"`
					}

					err := propColl.FindOne(ctx, bson.M{"_id": lease.PropID}).Decode(&property)
					if err == nil {
						tenant.PropertyName = property.Name
					}
				}
			}
		}

		tenants = append(tenants, tenant)
	}

	return tenants, total, nil
}

// GetAllTenants 获取所有租户
func GetAllTenants(ctx context.Context) ([]Tenant, error) {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return nil, fmt.Errorf("tenants collection not initialized")
	}

	cursor, err := tenantColl.Find(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to find tenants: %v", err)
	}
	defer cursor.Close(ctx)

	var tenants []Tenant
	if err := cursor.All(ctx, &tenants); err != nil {
		return nil, fmt.Errorf("failed to decode tenants: %v", err)
	}

	return tenants, nil
}

// GetTenantByID 根据ID获取租户
func GetTenantByID(ctx context.Context, tenantID string) (*Tenant, error) {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return nil, fmt.Errorf("tenants collection not initialized")
	}

	var tenant Tenant
	err := tenantColl.FindOne(ctx, bson.M{"_id": tenantID}).Decode(&tenant)
	if err != nil {
		return nil, fmt.Errorf("tenant not found")
	}

	return &tenant, nil
}

// UpdateFields 更新租户信息
func (t *Tenant) UpdateFields(ctx context.Context, updates map[string]interface{}) error {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return fmt.Errorf("tenants collection not initialized")
	}

	// 添加访问控制检查
	scope, err := GetResourceScope(ctx, t.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = t.ID

	update := bson.M{
		"$set": updates,
	}

	result, err := tenantColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update tenant: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("tenant not found or access denied")
	}

	return nil
}

// Delete 删除租户
func (t *Tenant) Delete(ctx context.Context) error {
	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return fmt.Errorf("tenants collection not initialized")
	}

	_, err := tenantColl.DeleteOne(ctx, bson.M{"_id": t.ID})
	if err != nil {
		return fmt.Errorf("failed to delete tenant: %v", err)
	}

	return nil
}

// handleNewTenantRentReportingNotification 处理新租户添加时的租金报告启用邮件通知
func handleNewTenantRentReportingNotification(ctx context.Context, tenant *Tenant) error {
	// 获取租约信息以检查 rent reporting 状态
	lease, err := GetLeaseByID(ctx, tenant.LeaseID)
	if err != nil {
		return fmt.Errorf("failed to get lease info: %v", err)
	}

	// 只有当 rent reporting 启用时才发送邮件
	if !lease.RentReporting {
		return nil
	}

	// 获取房东信息
	user, err := GetUserByID(ctx, tenant.UserID)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := GetProperty(ctx, lease.PropertyID, tenant.UserID)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", lease.PropertyID)
		// 不返回错误，继续发送邮件
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,   // 使用 username 作为名字
		LastName:        "",              // 没有单独的姓氏字段
		PropertyAddress: "your property", // 默认值
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	// 准备新租户的邮件信息
	if tenant.Email != "" && tenant.FirstName != "" {
		tenants := []utils.TenantEmailInfo{
			{
				Email:     tenant.Email,
				FirstName: tenant.FirstName,
			},
		}

		// 异步发送启用邮件给新租户
		utils.SendRentReportingNotificationEmailsAsync(tenants, landlordInfo)
		golog.Info("Initiated rent reporting enabled notification email for new tenant",
			"tenantId", tenant.ID,
			"leaseId", tenant.LeaseID,
			"tenantEmail", tenant.Email,
			"landlordName", landlordInfo.FirstName,
			"propertyAddress", landlordInfo.PropertyAddress,
			"propertyId", lease.PropertyID)
	} else {
		golog.Warn("New tenant missing email or first name for rent reporting notification", "tenantId", tenant.ID, "leaseId", tenant.LeaseID)
	}

	return nil
}

// handleNewTenantRentReportingNotificationDelayed 处理新租户添加时的延迟租金报告通知
func handleNewTenantRentReportingNotificationDelayed(ctx context.Context, tenant *Tenant) error {
	golog.Info("Starting delayed rent reporting notification for new tenant",
		"tenantId", tenant.ID,
		"leaseId", tenant.LeaseID,
		"tenantEmail", tenant.Email)

	// 获取租约信息以检查 rent reporting 状态
	lease, err := GetLeaseByID(ctx, tenant.LeaseID)
	if err != nil {
		golog.Error("Failed to get lease info for delayed notification",
			"error", err,
			"tenantId", tenant.ID,
			"leaseId", tenant.LeaseID)
		return fmt.Errorf("failed to get lease info: %v", err)
	}

	golog.Info("Retrieved lease info for delayed notification",
		"leaseId", lease.ID,
		"rentReporting", lease.RentReporting,
		"leaseStatus", lease.Status)

	// 只有当 rent reporting 启用时才处理
	if !lease.RentReporting {
		golog.Info("Rent reporting not enabled for lease, skipping delayed notification",
			"leaseId", lease.ID,
			"tenantId", tenant.ID)
		return nil
	}

	// 获取延迟时间配置（统一从配置文件读取）
	delayMinutes := getDelayMinutesFromConfig()

	golog.Info("Delay configuration loaded",
		"delayMinutes", delayMinutes,
		"configValue", goconfig.Config("delayedEmailNotification.delayMinutes"),
		"configType", fmt.Sprintf("%T", goconfig.Config("delayedEmailNotification.delayMinutes")))

	// 更新租金报告邮件队列（新的批处理方式）
	// 双四宫格逻辑：添加进ctnt时，只有租约开启才是active，否则是inactive
	finalStatus := "inactive"
	if lease.RentReporting {
		finalStatus = "active"
	}
	utils.UpdateRentReportEmailQueue(ctx, tenant.ID, tenant.Email, tenant.FirstName, tenant.LeaseID, lease.PropertyID, lease.UserID, finalStatus)

	golog.Info("Scheduled delayed rent reporting notification for new tenant",
		"tenantId", tenant.ID,
		"leaseId", tenant.LeaseID,
		"tenantEmail", tenant.Email,
		"delayMinutes", delayMinutes)

	return nil
}

// handleDeletedTenantRentReportingNotification 处理租户删除时的租金报告暂停邮件通知
func handleDeletedTenantRentReportingNotification(ctx context.Context, tenant *Tenant) error {
	// 获取租约信息以检查 rent reporting 状态
	lease, err := GetLeaseByID(ctx, tenant.LeaseID)
	if err != nil {
		return fmt.Errorf("failed to get lease info: %v", err)
	}

	// 只有当 rent reporting 启用时才发送邮件
	if !lease.RentReporting {
		return nil
	}

	// 获取房东信息
	user, err := GetUserByID(ctx, tenant.UserID)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := GetProperty(ctx, lease.PropertyID, tenant.UserID)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", lease.PropertyID)
		// 不返回错误，继续发送邮件
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,   // 使用 username 作为名字
		LastName:        "",              // 没有单独的姓氏字段
		PropertyAddress: "your property", // 默认值
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	// 准备被删除租户的邮件信息
	if tenant.Email != "" && tenant.FirstName != "" {
		tenants := []utils.TenantEmailInfo{
			{
				Email:     tenant.Email,
				FirstName: tenant.FirstName,
			},
		}

		// 异步发送暂停邮件给被删除的租户
		utils.SendRentReportingPausedNotificationEmailsAsync(tenants, landlordInfo)
		golog.Info("Initiated rent reporting paused notification email for deleted tenant",
			"tenantId", tenant.ID,
			"leaseId", tenant.LeaseID,
			"tenantEmail", tenant.Email,
			"landlordName", landlordInfo.FirstName,
			"propertyAddress", landlordInfo.PropertyAddress,
			"propertyId", lease.PropertyID)
	} else {
		golog.Warn("Deleted tenant missing email or first name for rent reporting notification", "tenantId", tenant.ID, "leaseId", tenant.LeaseID)
	}

	return nil
}

// handleDeletedTenantRentReportingNotificationDelayed 处理租户删除时的延迟租金报告通知
func handleDeletedTenantRentReportingNotificationDelayed(ctx context.Context, tenant *Tenant) error {
	// 获取租约信息以检查 rent reporting 状态
	lease, err := GetLeaseByID(ctx, tenant.LeaseID)
	if err != nil {
		return fmt.Errorf("failed to get lease info: %v", err)
	}

	// 只有当 rent reporting 启用时才处理
	if !lease.RentReporting {
		return nil
	}

	// 获取延迟时间配置（统一从配置文件读取）
	delayMinutes := getDelayMinutesFromConfig()

	// 更新租金报告邮件队列（新的批处理方式）
	// 双四宫格逻辑：从ctnt删除时，无论租约开关状态都是inactive
	utils.UpdateRentReportEmailQueue(ctx, tenant.ID, tenant.Email, tenant.FirstName, tenant.LeaseID, lease.PropertyID, lease.UserID, "inactive")

	golog.Info("Scheduled delayed rent reporting notification for deleted tenant",
		"tenantId", tenant.ID,
		"leaseId", tenant.LeaseID,
		"tenantEmail", tenant.Email,
		"delayMinutes", delayMinutes)

	return nil
}

// handleRestoredTenantRentReportingNotificationDelayed 处理恢复租户的延迟租金报告通知
func handleRestoredTenantRentReportingNotificationDelayed(ctx context.Context, tenant *Tenant, lease *Lease) error {
	golog.Info("Starting delayed rent reporting notification for restored tenant",
		"tenantId", tenant.ID,
		"leaseId", lease.ID,
		"tenantEmail", tenant.Email)

	// 获取延迟时间配置（统一从配置文件读取）
	delayMinutes := getDelayMinutesFromConfig()

	golog.Info("Delay configuration loaded for restored tenant",
		"delayMinutes", delayMinutes,
		"configValue", goconfig.Config("delayedEmailNotification.delayMinutes"),
		"configType", fmt.Sprintf("%T", goconfig.Config("delayedEmailNotification.delayMinutes")))

	// 更新租金报告邮件队列（新的批处理方式）
	// 双四宫格逻辑：恢复到ctnt时，只有租约开启才是active，否则是inactive
	finalStatus := "inactive"
	if lease.RentReporting {
		finalStatus = "active"
	}
	utils.UpdateRentReportEmailQueue(ctx, tenant.ID, tenant.Email, tenant.FirstName, tenant.LeaseID, lease.PropertyID, lease.UserID, finalStatus)

	golog.Info("Scheduled delayed rent reporting notification for restored tenant",
		"tenantId", tenant.ID,
		"leaseId", tenant.LeaseID,
		"tenantEmail", tenant.Email,
		"delayMinutes", delayMinutes)

	return nil
}
