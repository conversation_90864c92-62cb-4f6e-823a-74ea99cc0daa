package entities

import (
	"context"
	"fmt"
	"log"
	"rent_report/config"
	"rent_report/utils"
	"strings"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// LeaseDocument 表示租约文档
type LeaseDocument struct {
	ID            string     `json:"id" bson:"id"`                                   // 文档唯一标识
	FilePath      string     `json:"filePath" bson:"filePath"`                       // goupload返回的文件路径
	FileName      string     `json:"fileName" bson:"fileName"`                       // 原始文件名
	GeneratedName string     `json:"generatedName" bson:"generatedName"`             // goupload生成的文件名
	FileSize      int64      `json:"fileSize" bson:"fileSize"`                       // 文件大小
	FileType      string     `json:"fileType" bson:"fileType"`                       // 文件类型/MIME类型
	UploadedAt    time.Time  `json:"uploadedAt" bson:"uploadedAt"`                   // 上传时间
	UploadedBy    string     `json:"uploadedBy" bson:"uploadedBy"`                   // 上传用户ID
	Status        string     `json:"status" bson:"status"`                           // 文件状态: active, deleted, final_deleted
	DeletedAt     *time.Time `json:"deletedAt,omitempty" bson:"deletedAt,omitempty"` // 删除时间
	IsProtected   bool       `json:"isProtected" bson:"isProtected"`                 // 是否受保护，不可删除（如用于Metro2报告）
}

type Lease struct {
	ID                    string   `json:"id" bson:"_id"`
	PropertyID            string   `json:"propertyId" bson:"propId"`
	RoomID                string   `json:"roomId" bson:"roomId"`
	UserID                string   `json:"-" bson:"usrId"`
	TenantID              string   `json:"tenantId" bson:"tenantId"`
	RentAmount            float64  `json:"rentAmount" bson:"rentAm"`
	StartDate             string   `json:"startDate" bson:"startDt"`
	EndDate               string   `json:"endDate" bson:"endDt"`
	Status                string   `json:"status" bson:"status"`
	AdditionalMonthlyFees float64  `json:"additionalMonthlyFees" bson:"addFees"`
	KeyDeposit            float64  `json:"keyDeposit" bson:"keyDep"`
	RentDeposit           float64  `json:"rentDeposit" bson:"rentDep"`
	OtherDeposits         float64  `json:"otherDeposits" bson:"otherDep"`
	RentDueDay            int      `json:"rentDueDay" bson:"rentDueDay"`
	RentReporting         bool     `json:"rentReporting" bson:"rentRep"`
	AutoPay               bool     `json:"autoPay" bson:"autoPay"`
	OwingBalance          float64  `json:"owingBalance" bson:"owingBal"`
	LastPaymentDate       string   `json:"lastPaymentDate" bson:"lastPmtDt"`
	OrganizationID        string   `json:"-" bson:"orgId,omitempty"`
	PropertyName          string   `json:"propertyName" bson:"-"`
	RoomName              string   `json:"roomName" bson:"-"`
	PropertyAddress       string   `json:"propertyAddress" bson:"-"`
	LandlordName          string   `json:"landlordName" bson:"-"`
	LandlordEmail         string   `json:"landlordEmail" bson:"-"`
	LandlordPhone         string   `json:"landlordPhone" bson:"-"`
	InvId                 string   `json:"invId" bson:"invId"`
	CurrentTenants        []Tenant `json:"currentTenants" bson:"ctnts"`
	PastTenants           []Tenant `json:"pastTenants" bson:"ptnts"`
	// 单文件字段已移除，只使用documents数组
	// 新的多文件字段
	Documents []LeaseDocument `json:"documents" bson:"documents"`
	Notes     string          `json:"notes" bson:"notes"`
	// 权限标识字段（不存储在数据库中，仅用于API响应）
	IsCurrentUserLandlord bool `json:"isCurrentUserLandlord" bson:"-"`
}

type LeaseList struct {
	ID              string             `json:"id" bson:"_id"`
	PropertyID      string             `json:"propertyId" bson:"propId"`
	RoomID          string             `json:"roomId" bson:"roomId"`
	UserID          string             `json:"userId" bson:"usrId"`
	TenantID        string             `json:"tenantId" bson:"tenantId"`
	Status          string             `json:"status" bson:"status"`
	OwingBalance    float64            `json:"owingBalance" bson:"owingBal"`
	LastPaymentDate string             `json:"lastPaymentDate" bson:"lastPmtDt"`
	StartDate       string             `json:"startDate" bson:"startDt"`
	EndDate         string             `json:"endDate" bson:"endDt"`
	PropertyName    string             `json:"propertyName" bson:"-"`
	RoomName        string             `json:"roomName" bson:"-"`
	TenantName      string             `json:"tenantName" bson:"-"`
	LandlordName    string             `json:"landlordName" bson:"-"`
	CurrentTenants  []Tenant           `json:"currentTenants" bson:"ctnts"`
	PastTenants     []Tenant           `json:"pastTenants" bson:"ptnts"`
	Ts              primitive.DateTime `json:"_ts" bson:"_ts"`
	PropertyAddress string             `json:"propertyAddress" bson:"-"`
	RentRep         bool               `json:"rentRep" bson:"rentRep"`
}

func GetLeases(ctx context.Context, limit int, filters map[string]string) ([]LeaseList, int64, error) {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, 0, fmt.Errorf("leases collection not initialized")
	}

	scope, err := GetResourceScope(ctx, filters["userId"])
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get resource scope: %v", err)
	}

	// Build query filter
	filter := scope.ToFilter()
	userId := filters["userId"]
	role := filters["role"]
	if userId != "" {
		delete(filter, "usrId")
		if role == "landlord" {
			filter["usrId"] = userId
		} else if role == "tenant" {
			filter["$or"] = []bson.M{
				{"ctnts.tenantId": userId},
				{"ptnts.tenantId": userId},
			}
		} else {
			filter["$or"] = []bson.M{
				{"usrId": userId},
				{"ctnts.tenantId": userId},
				{"ptnts.tenantId": userId},
			}
		}
	}
	if propertyId := filters["propertyId"]; propertyId != "" {
		filter["propId"] = propertyId
	}
	if status := filters["status"]; status != "" {
		filter["status"] = status
	}
	if invId := filters["invId"]; invId != "" {
		filter["invId"] = invId
		delete(filter, "usrId")
		delete(filter, "tenantId")
		delete(filter, "$or")
	}

	// 打印调试信息
	golog.Debug("GetLeases", "userId", userId, "role", role, "filter", filter)

	// Get total count
	total, err := leaseColl.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	findOptions := gomongo.QueryOptions{
		Limit: int64(limit),
		Sort:  bson.D{{Key: "_id", Value: -1}},
		Projection: bson.M{
			"_id":       1,
			"propId":    1,
			"roomId":    1,
			"status":    1,
			"owingBal":  1,
			"lastPmtDt": 1,
			"tenantId":  1,
			"usrId":     1,
			"startDt":   1,
			"endDt":     1,
			"ctnts":     1,
			"ptnts":     1,
			"_ts":       1,
			"rentRep":   1,
		},
	}

	// Initialize leases as empty slice instead of nil
	leases := make([]LeaseList, 0)

	result, err := leaseColl.FindToArray(ctx, filter, findOptions)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to execute find query: %v", err)
	}

	// 获取所有相关的 property 和 tenant 信息
	propertyIDs := make([]string, 0)
	tenantIDs := make([]string, 0)
	landlordIDs := make([]string, 0)
	for _, item := range result {
		var lease LeaseList
		data, err := bson.Marshal(item)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to marshal lease data: %v", err)
		}

		if err := bson.Unmarshal(data, &lease); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal lease data: %v", err)
		}

		propertyIDs = append(propertyIDs, lease.PropertyID)
		if lease.TenantID != "" {
			tenantIDs = append(tenantIDs, lease.TenantID)
		}
		if lease.UserID != "" {
			landlordIDs = append(landlordIDs, lease.UserID)
		}
		leases = append(leases, lease)
	}

	// 获取所有相关的 property 信息
	propertyMap := make(map[string]struct {
		Name    string
		Room    map[string]string
		Address map[string]interface{}
	})
	if propColl := gomongo.Coll("rr", "properties"); propColl != nil {
		propFilter := bson.M{"_id": bson.M{"$in": propertyIDs}}
		propResult, err := propColl.FindToArray(ctx, propFilter, gomongo.QueryOptions{
			Projection: bson.M{
				"_id":   1,
				"nm":    1,
				"rooms": 1,
				"addr":  1,
			},
		})
		if err == nil {
			for _, prop := range propResult {
				var property struct {
					ID    string `bson:"_id"`
					Name  string `bson:"nm"`
					Rooms []struct {
						ID   string `bson:"_id"`
						Name string `bson:"nm"`
					} `bson:"rooms"`
					Address map[string]interface{} `bson:"addr"`
				}
				data, err := bson.Marshal(prop)
				if err != nil {
					continue
				}
				if err := bson.Unmarshal(data, &property); err == nil {
					roomMap := make(map[string]string)
					for _, room := range property.Rooms {
						roomMap[room.ID] = room.Name
					}
					propertyMap[property.ID] = struct {
						Name    string
						Room    map[string]string
						Address map[string]interface{}
					}{
						Name:    property.Name,
						Room:    roomMap,
						Address: property.Address,
					}
				}
			}
		}
	}

	// 获取所有相关的 tenant 信息
	tenantMap := make(map[string]string)
	if len(tenantIDs) > 0 {
		tenantColl := gomongo.Coll("rr", "tenants")
		if tenantColl != nil {
			tenantFilter := bson.M{"_id": bson.M{"$in": tenantIDs}}
			tenantResult, err := tenantColl.FindToArray(ctx, tenantFilter, gomongo.QueryOptions{
				Projection: bson.M{
					"_id":     1,
					"firstNm": 1,
					"lastNm":  1,
				},
			})
			if err == nil {
				for _, tenant := range tenantResult {
					var t struct {
						ID        string `bson:"_id"`
						FirstName string `bson:"firstNm"`
						LastName  string `bson:"lastNm"`
					}
					data, err := bson.Marshal(tenant)
					if err != nil {
						continue
					}
					if err := bson.Unmarshal(data, &t); err == nil {
						tenantMap[t.ID] = t.FirstName + " " + t.LastName
					}
				}
			}
		}
	}

	// 获取所有相关的 landlord 信息
	landlordMap := make(map[string]string)
	if len(landlordIDs) > 0 {
		userColl := gomongo.Coll("rr", "users")
		if userColl != nil {
			userFilter := bson.M{"_id": bson.M{"$in": landlordIDs}}
			userResult, err := userColl.FindToArray(ctx, userFilter, gomongo.QueryOptions{
				Projection: bson.M{
					"_id":   1,
					"usrNm": 1,
					"email": 1,
				},
			})
			if err == nil {
				for _, user := range userResult {
					var u struct {
						ID    string `bson:"_id"`
						UsrNm string `bson:"usrNm"`
						Email string `bson:"email"`
					}
					data, err := bson.Marshal(user)
					if err != nil {
						continue
					}
					if err := bson.Unmarshal(data, &u); err == nil {
						// 如果usrNm为空，使用email作为landlord名称
						if u.UsrNm != "" {
							landlordMap[u.ID] = u.UsrNm
						} else {
							landlordMap[u.ID] = u.Email
						}
					}
				}
			}
		}
	}

	// 填充关联信息
	for i := range leases {
		if prop, ok := propertyMap[leases[i].PropertyID]; ok {
			leases[i].PropertyName = prop.Name
			if roomName, ok := prop.Room[leases[i].RoomID]; ok {
				leases[i].RoomName = roomName
			}
			// 格式化address
			addr := prop.Address
			addrParts := []string{}
			if s, ok := addr["street"].(string); ok && s != "" {
				addrParts = append(addrParts, s)
			}
			if u, ok := addr["unit"].(string); ok && u != "" {
				addrParts = append(addrParts, u)
			}
			if c, ok := addr["city"].(string); ok && c != "" {
				addrParts = append(addrParts, c)
			}
			if p, ok := addr["prov"].(string); ok && p != "" {
				addrParts = append(addrParts, p)
			}
			if co, ok := addr["country"].(string); ok && co != "" {
				addrParts = append(addrParts, co)
			}
			if z, ok := addr["zipCode"].(string); ok && z != "" {
				addrParts = append(addrParts, z)
			} else if z, ok := addr["zip"].(string); ok && z != "" {
				addrParts = append(addrParts, z)
			}
			leases[i].PropertyAddress = strings.Join(addrParts, ", ")
		}
		// 只用CurrentTenants[0]作为primary tenant
		if len(leases[i].CurrentTenants) > 0 {
			leases[i].TenantName = leases[i].CurrentTenants[0].FirstName + " " + leases[i].CurrentTenants[0].LastName
		} else {
			leases[i].TenantName = "-"
		}
		if landlordName, ok := landlordMap[leases[i].UserID]; ok {
			leases[i].LandlordName = landlordName
		}
	}

	golog.Debug("Leases result", "count", len(leases), "total", total)
	return leases, total, nil
}

func (lease *Lease) Create(ctx context.Context) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// Get resource scope for access control
	scope, err := GetResourceScope(ctx, lease.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	// Set organization based on scope
	lease.OrganizationID = scope.OrganizationID

	// Validate required fields
	if lease.ID == "" || lease.UserID == "" || lease.PropertyID == "" || lease.RoomID == "" ||
		lease.RentAmount == 0 || lease.StartDate == "" {
		return fmt.Errorf("id, userId, propertyId, roomId, rentAmount, startDate are required")
	}

	// Validate status if provided
	if lease.Status != "" {
		validStatus := map[string]bool{
			"active":  true,
			"ended":   true,
			"deleted": true,
			"pending": true,
		}
		if !validStatus[lease.Status] {
			return fmt.Errorf("invalid status")
		}
	}

	// Normalize rentDueDay if provided
	if lease.RentDueDay != 0 {
		lease.RentDueDay = normalizeRentDueDay(lease.RentDueDay)
	}

	// Scenario 1: Set initial owing balance
	// 如果前端提供了 owingBalance，使用它；否则自动计算
	if lease.OwingBalance > 0 {
		// 使用前端提供的值，不做修改
	} else {
		// 自动计算
		lease.OwingBalance = calculateInitialOwingBalance(lease)
	}

	_, err = leaseColl.InsertOne(ctx, lease)
	if err != nil {
		return fmt.Errorf("failed to insert lease: %v", err)
	}

	return nil
}

func GetLease(ctx context.Context, id string, userID string) (*Lease, error) {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	// 允许userId是usrId或ctnts/ptnts中的tenantId都能查
	filter := bson.M{
		"_id": id,
		"$or": []bson.M{
			{"usrId": userID},
			{"ctnts.tenantId": userID},
			{"ptnts.tenantId": userID},
		},
	}

	result := leaseColl.FindOne(ctx, filter)
	if result.Err() != nil {
		// 新增：如果没查到，再用id单独查一次（只读模式）
		result = leaseColl.FindOne(ctx, bson.M{"_id": id})
		if result.Err() != nil {
			if result.Err() == mongo.ErrNoDocuments {
				return nil, fmt.Errorf("lease not found")
			}
			return nil, fmt.Errorf("failed to get lease: %v", result.Err())
		}
	}

	var lease Lease
	if err := result.Decode(&lease); err != nil {
		return nil, fmt.Errorf("failed to decode lease: %v", err)
	}

	// 获取 property 信息
	propColl := gomongo.Coll("rr", "properties")
	if propColl != nil {
		propFilter := bson.M{"_id": lease.PropertyID}
		var property struct {
			Name  string `bson:"nm"`
			Rooms []struct {
				ID   string `bson:"_id"`
				Name string `bson:"nm"`
			} `bson:"rooms"`
			Address struct {
				Street  string `bson:"street"`
				City    string `bson:"city"`
				Prov    string `bson:"prov"`
				Country string `bson:"country"`
				ZipCode string `bson:"zip"`
				Unit    string `bson:"unit"`
			} `bson:"addr"`
		}
		if err := propColl.FindOne(ctx, propFilter).Decode(&property); err == nil {
			lease.PropertyName = property.Name
			// 格式化地址
			addr := property.Address
			addressStr := ""
			if addr.Street != "" {
				addressStr += addr.Street
			}
			if addr.City != "" {
				if addressStr != "" {
					addressStr += ", "
				}
				addressStr += addr.City
			}
			if addr.Prov != "" {
				if addressStr != "" {
					addressStr += ", "
				}
				addressStr += addr.Prov
			}
			if addr.ZipCode != "" {
				if addressStr != "" {
					addressStr += " "
				}
				addressStr += addr.ZipCode
			}
			if addr.Country != "" {
				if addressStr != "" {
					addressStr += ", "
				}
				addressStr += addr.Country
			}
			lease.PropertyAddress = addressStr
			for _, room := range property.Rooms {
				if room.ID == lease.RoomID {
					lease.RoomName = room.Name
					break
				}
			}
		}
	}

	// 获取 landlord 信息
	userColl := gomongo.Coll("rr", "users")
	if userColl != nil && lease.UserID != "" {
		userFilter := bson.M{"_id": lease.UserID}
		var user struct {
			UsrNm string `bson:"usrNm"`
			Email string `bson:"email"`
			Phone string `bson:"phoneNumber"`
		}
		if err := userColl.FindOne(ctx, userFilter).Decode(&user); err == nil {
			// 如果usrNm为空，使用email作为LandlordName
			if user.UsrNm != "" {
				lease.LandlordName = user.UsrNm
			} else {
				lease.LandlordName = user.Email
			}
			lease.LandlordEmail = user.Email
			lease.LandlordPhone = user.Phone
		}
	}

	// 设置权限标识：检查当前用户是否是租约的房东
	lease.IsCurrentUserLandlord = (lease.UserID == userID)

	return &lease, nil
}

func (lease *Lease) Update(ctx context.Context) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	scope, err := GetResourceScope(ctx, lease.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = lease.ID

	// Get original lease data for comparison (Scenario 6)
	var originalLease Lease
	err = leaseColl.FindOne(ctx, filter).Decode(&originalLease)
	if err != nil {
		return fmt.Errorf("failed to find original lease: %v", err)
	}

	// Scenario 5: Check if lease allows modifications
	err = validateLeaseCanBeModified(ctx, lease.ID, lease, scope)
	if err != nil {
		return fmt.Errorf("lease modification validation failed: %v", err)
	}

	// Set organization based on scope
	lease.OrganizationID = scope.OrganizationID

	// Validate required fields
	if lease.ID == "" || lease.UserID == "" {
		return fmt.Errorf("id and userId are required")
	}

	// Validate status if provided
	if lease.Status != "" {
		validStatus := map[string]bool{
			"active":  true,
			"ended":   true,
			"deleted": true,
			"pending": true,
		}
		if !validStatus[lease.Status] {
			return fmt.Errorf("invalid status")
		}
	}

	// Validate rentDueDay if provided
	if lease.RentDueDay != 0 && (lease.RentDueDay < 1 || lease.RentDueDay > 31) {
		return fmt.Errorf("rentDueDay must be between 1 and 31")
	}

	// Convert lease to BSON document for update
	updateDoc, err := gomongo.ToBSONDoc(lease)
	if err != nil {
		return fmt.Errorf("failed to create update document: %v", err)
	}

	result, err := leaseColl.UpdateOne(ctx, filter, bson.M{"$set": updateDoc})
	if err != nil {
		return fmt.Errorf("failed to update lease: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("lease not found")
	}

	// Scenario 6: Handle rent amount changes
	err = handleRentAmountChange(ctx, &originalLease, lease, scope)
	if err != nil {
		return fmt.Errorf("failed to handle rent amount change: %v", err)
	}

	// Scenario 9: Handle rent due day changes
	err = handleRentDueDayChange(ctx, &originalLease, lease)
	if err != nil {
		return fmt.Errorf("failed to handle rent due day change: %v", err)
	}

	// Handle rent reporting notification emails (new batch processing)
	if originalLease.RentReporting != lease.RentReporting {
		// 租约开关状态变化，更新所有当前租户的邮件队列
		for _, tenant := range lease.CurrentTenants {
			if tenant.Email != "" && tenant.FirstName != "" {
				// 计算初始状态和最终状态
				initialStatus := "inactive"
				if originalLease.RentReporting {
					initialStatus = "active"
				}

				finalStatus := "inactive"
				if lease.RentReporting {
					finalStatus = "active"
				}

				golog.Info("Rent reporting status change",
					"tenantId", tenant.ID,
					"originalRentReporting", originalLease.RentReporting,
					"newRentReporting", lease.RentReporting,
					"initialStatus", initialStatus,
					"finalStatus", finalStatus)

				utils.UpdateRentReportEmailQueueWithInitialStatus(ctx, tenant.ID, tenant.Email, tenant.FirstName, lease.ID, lease.PropertyID, lease.UserID, initialStatus, finalStatus)
			}
		}
	}

	return nil
}

// deleteLeaseFiles 删除lease的所有文件（实际文件和数据库记录）
func deleteLeaseFiles(ctx context.Context, lease *Lease) error {
	if len(lease.Documents) == 0 {
		return nil // 没有文件需要删除
	}

	// 加载上传配置
	uploadConfig := config.LoadUploadConfig()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, uploadConfig.LeaseDocuments, statsColl)
	if err != nil {
		return fmt.Errorf("failed to create stats updater: %v", err)
	}

	// 删除所有文件
	var failedFiles []string
	for _, doc := range lease.Documents {
		if doc.FilePath == "" {
			golog.Warn("Lease document has empty file path", "leaseId", lease.ID, "documentId", doc.ID)
			continue
		}

		// 调用goupload删除文件
		result, err := goupload.Delete(ctx, statsUpdater, uploadConfig.Site, uploadConfig.LeaseDocuments, doc.FilePath)
		if err != nil {
			golog.Error("Failed to delete lease file", "error", err, "leaseId", lease.ID, "filePath", doc.FilePath)
			failedFiles = append(failedFiles, doc.FilePath)
			continue
		}

		// 记录删除结果
		if result != nil && len(result.FailedPaths) > 0 {
			golog.Warn("Some files failed to delete", "leaseId", lease.ID, "failedCount", len(result.FailedPaths))
			for _, fp := range result.FailedPaths {
				failedFiles = append(failedFiles, fp.Path)
			}
		} else {
			golog.Info("Successfully deleted lease file", "leaseId", lease.ID, "filePath", doc.FilePath)
		}
	}

	// 如果有文件删除失败，返回错误
	if len(failedFiles) > 0 {
		return fmt.Errorf("failed to delete %d file(s): %v", len(failedFiles), failedFiles)
	}

	return nil
}

// deleteLeasePayments 删除lease的所有payment记录
func deleteLeasePayments(ctx context.Context, leaseID string, userID string) error {
	var paymentColl = gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return fmt.Errorf("tenant_payments collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	// 构建删除条件
	filter := scope.ToFilter()
	filter["leaseId"] = leaseID

	// 删除所有相关的payment记录
	result, err := paymentColl.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete lease payments: %v", err)
	}

	golog.Info("Successfully deleted lease payments", "leaseId", leaseID, "deletedCount", result.DeletedCount)
	return nil
}

func DeleteLease(ctx context.Context, id string, userID string) error {
	// 首先检查删除约束
	if err := CheckLeaseDeletionConstraints(ctx, id, userID); err != nil {
		return err
	}

	// 获取lease信息（包含documents）
	lease, err := GetLease(ctx, id, userID)
	if err != nil {
		return fmt.Errorf("failed to get lease: %v", err)
	}

	// 1. 先删除所有文件
	if err := deleteLeaseFiles(ctx, lease); err != nil {
		golog.Error("Failed to delete lease files", "error", err, "leaseId", id)
		return fmt.Errorf("failed to delete lease files: %v", err)
	}

	// 2. 删除所有payment记录
	if err := deleteLeasePayments(ctx, id, userID); err != nil {
		golog.Error("Failed to delete lease payments", "error", err, "leaseId", id)
		return fmt.Errorf("failed to delete lease payments: %v", err)
	}

	// 3. 最后删除lease记录
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = id

	result, err := leaseColl.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete lease: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("lease not found")
	}

	golog.Info("Successfully deleted lease and all related data", "leaseId", id, "fileCount", len(lease.Documents))
	return nil
}

func UpdateLeaseTenant(ctx context.Context, leaseId, userId string, updatedTenant Tenant) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}
	scope, err := GetResourceScope(ctx, userId)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}
	filter := scope.ToFilter()
	filter["_id"] = leaseId
	update := bson.M{
		"$set": bson.M{
			"ctnts.$[elem]": updatedTenant,
		},
	}
	arrayFilters := options.ArrayFilters{
		Filters: []interface{}{bson.M{"elem._id": updatedTenant.ID}},
	}
	opts := options.Update().SetArrayFilters(arrayFilters)
	result, err := leaseColl.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to update tenant in lease: %v", err)
	}
	if result.MatchedCount == 0 {
		return fmt.Errorf("lease or tenant not found")
	}
	return nil
}

// RestoreLeaseTenant 将past tenant恢复到current tenants
func RestoreLeaseTenant(ctx context.Context, leaseId, userId, tenantId string) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}
	scope, err := GetResourceScope(ctx, userId)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}
	filter := scope.ToFilter()
	filter["_id"] = leaseId

	// 先查出要恢复的租客
	var lease Lease
	if err := leaseColl.FindOne(ctx, filter).Decode(&lease); err != nil {
		return fmt.Errorf("lease not found")
	}

	var restoredTenant *Tenant
	newPtnts := make([]Tenant, 0)
	for _, t := range lease.PastTenants {
		if t.ID == tenantId {
			tmp := t
			restoredTenant = &tmp
		} else {
			newPtnts = append(newPtnts, t)
		}
	}
	if restoredTenant == nil {
		return fmt.Errorf("tenant not found in past tenants")
	}

	// 如果 ctnts 为 nil，先初始化为空数组
	if lease.CurrentTenants == nil {
		_, _ = leaseColl.UpdateOne(ctx, filter, bson.M{"$set": bson.M{"ctnts": []Tenant{}}})
	}

	// 更新ptnts和ctnts
	update := bson.M{
		"$set":  bson.M{"ptnts": newPtnts},
		"$push": bson.M{"ctnts": restoredTenant},
	}
	res, err := leaseColl.UpdateOne(ctx, filter, update)
	golog.Debug("Restore tenant update result", "matchedCount", res.MatchedCount, "modifiedCount", res.ModifiedCount, "error", err)
	if err != nil {
		return fmt.Errorf("failed to restore tenant: %v", err)
	}

	// 恢复操作：从ptnt恢复到ctnt
	// 初始状态总是inactive（在ptnt），最终状态根据双四宫格逻辑
	// 但只有租约开启时才需要发送邮件通知
	if lease.RentReporting {
		initialStatus := "inactive" // 从ptnt恢复时，初始状态总是inactive
		finalStatus := "active"     // 恢复到ctnt且租约开启时才是active
		utils.UpdateRentReportEmailQueueWithInitialStatus(ctx, restoredTenant.ID, restoredTenant.Email, restoredTenant.FirstName, lease.ID, lease.PropertyID, lease.UserID, initialStatus, finalStatus)
	}

	return nil
}

// HardDeleteLeaseTenant 硬删除lease中的tenant（从ctnts或ptnts中删除，同时删除tenant表中的记录）
func HardDeleteLeaseTenant(ctx context.Context, leaseId, userId, tenantId string) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	var tenantColl = gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return fmt.Errorf("tenants collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userId)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	// 1. 先检查tenant是否受保护
	var tenant Tenant
	tenantFilter := scope.ToFilter()
	tenantFilter["_id"] = tenantId

	err = tenantColl.FindOne(ctx, tenantFilter).Decode(&tenant)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return fmt.Errorf("tenant record not found")
		}
		return fmt.Errorf("failed to get tenant: %v", err)
	}

	if tenant.IsProtected {
		return fmt.Errorf("cannot delete protected tenant. This tenant is protected due to Metro2 reporting requirements")
	}

	// 2. 先获取lease信息，确定tenant在哪个数组中
	leaseFilter := scope.ToFilter()
	leaseFilter["_id"] = leaseId

	var lease Lease
	err = leaseColl.FindOne(ctx, leaseFilter).Decode(&lease)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return fmt.Errorf("lease not found")
		}
		return fmt.Errorf("failed to get lease: %v", err)
	}

	// 3. 确定tenant在current还是past数组中
	var isInCurrent, isInPast bool
	for _, t := range lease.CurrentTenants {
		if t.ID == tenantId {
			isInCurrent = true
			break
		}
	}
	for _, t := range lease.PastTenants {
		if t.ID == tenantId {
			isInPast = true
			break
		}
	}

	if !isInCurrent && !isInPast {
		return fmt.Errorf("tenant not found in this lease")
	}

	// 4. 从tenant表中删除tenant记录（如果失败，lease不会被修改）
	tenantResult, err := tenantColl.DeleteOne(ctx, tenantFilter)
	if err != nil {
		return fmt.Errorf("failed to delete tenant record: %v", err)
	}

	if tenantResult.DeletedCount == 0 {
		return fmt.Errorf("tenant record not found")
	}

	// 5. 从lease的相应数组中删除tenant
	var update bson.M
	if isInCurrent {
		update = bson.M{
			"$pull": bson.M{
				"ctnts": bson.M{"_id": tenantId},
			},
		}
	} else {
		update = bson.M{
			"$pull": bson.M{
				"ptnts": bson.M{"_id": tenantId},
			},
		}
	}

	result, err := leaseColl.UpdateOne(ctx, leaseFilter, update)
	if err != nil {
		// 如果lease更新失败，记录警告（tenant记录已经被删除）
		log.Printf("Warning: failed to remove tenant %s from lease %s after deleting tenant record: %v", tenantId, leaseId, err)
		return fmt.Errorf("failed to remove tenant from lease: %v", err)
	}

	if result.MatchedCount == 0 {
		log.Printf("Warning: lease %s not found after deleting tenant record %s", leaseId, tenantId)
		return fmt.Errorf("lease not found")
	}

	// 6. 如果租约启用了rent reporting，安排延迟通知邮件
	if lease.RentReporting {
		err = handleDeletedTenantRentReportingNotificationDelayed(ctx, &tenant)
		if err != nil {
			// 记录错误但不影响主要操作
			golog.Error("Failed to schedule delayed rent reporting paused notification for deleted tenant", "error", err, "tenantId", tenant.ID, "leaseId", leaseId)
		}
	}

	if result.ModifiedCount == 0 {
		log.Printf("Warning: tenant %s not found in lease %s ctnts after deleting tenant record", tenantId, leaseId)
		return fmt.Errorf("tenant not found in current tenants")
	}

	return nil
}

// CheckLeaseDeletionConstraints 检查lease是否可以被删除
// 返回错误信息如果有关联的tenant（包括current和past tenants）
func CheckLeaseDeletionConstraints(ctx context.Context, leaseID string, userID string) error {
	// 获取lease信息
	lease, err := GetLease(ctx, leaseID, userID)
	if err != nil {
		return fmt.Errorf("failed to get lease: %v", err)
	}

	// 检查是否有当前的tenant
	currentTenantCount := len(lease.CurrentTenants)
	pastTenantCount := len(lease.PastTenants)
	totalTenantCount := currentTenantCount + pastTenantCount

	if totalTenantCount > 0 {
		if currentTenantCount > 0 && pastTenantCount > 0 {
			return fmt.Errorf("cannot delete lease: lease has %d current tenant(s) and %d past tenant(s). Please delete all tenants first", currentTenantCount, pastTenantCount)
		} else if currentTenantCount > 0 {
			return fmt.Errorf("cannot delete lease: lease has %d current tenant(s). Please delete all tenants first", currentTenantCount)
		} else {
			return fmt.Errorf("cannot delete lease: lease has %d past tenant(s). Please delete all tenants first", pastTenantCount)
		}
	}

	return nil
}

func SoftDeleteLeaseTenant(ctx context.Context, leaseId, userId, tenantId string) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}
	scope, err := GetResourceScope(ctx, userId)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}
	filter := scope.ToFilter()
	filter["_id"] = leaseId

	// 先查出要删除的租客
	var lease Lease
	if err := leaseColl.FindOne(ctx, filter).Decode(&lease); err != nil {
		return fmt.Errorf("lease not found")
	}
	var removedTenant *Tenant
	newCtnts := make([]Tenant, 0)
	for _, t := range lease.CurrentTenants {
		if t.ID == tenantId {
			tmp := t
			removedTenant = &tmp
		} else {
			newCtnts = append(newCtnts, t)
		}
	}
	if removedTenant == nil {
		return fmt.Errorf("tenant not found in ctnts")
	}

	// 注意：软删除（移动到past tenants）不检查IsProtected，因为这不是真正的删除
	// 只有硬删除才检查IsProtected

	// 移动操作：从ctnt移动到ptnt
	// 初始状态根据双四宫格逻辑，最终状态总是inactive（移动到ptnt）
	// 但只有租约开启时才需要发送邮件通知
	if lease.RentReporting {
		initialStatus := "active" // 从ctnt移动且租约开启时，初始状态是active
		finalStatus := "inactive" // 移动到ptnt后总是inactive
		utils.UpdateRentReportEmailQueueWithInitialStatus(ctx, removedTenant.ID, removedTenant.Email, removedTenant.FirstName, lease.ID, lease.PropertyID, lease.UserID, initialStatus, finalStatus)
	}

	// 如果 ptnts 为 nil，先初始化为空数组
	if lease.PastTenants == nil {
		_, _ = leaseColl.UpdateOne(ctx, filter, bson.M{"$set": bson.M{"ptnts": []Tenant{}}})
	}

	// 更新ctnts和ptnts
	update := bson.M{
		"$set":  bson.M{"ctnts": newCtnts},
		"$push": bson.M{"ptnts": removedTenant},
	}
	res, err := leaseColl.UpdateOne(ctx, filter, update)
	golog.Debug("Soft delete tenant update result", "matchedCount", res.MatchedCount, "modifiedCount", res.ModifiedCount, "error", err)
	if err != nil {
		return fmt.Errorf("failed to soft delete tenant: %v", err)
	}
	return nil
}

// GetAllLeases 获取所有租约
func GetAllLeases(ctx context.Context) ([]Lease, error) {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	cursor, err := leaseColl.Find(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to find leases: %v", err)
	}
	defer cursor.Close(ctx)

	var leases []Lease
	if err := cursor.All(ctx, &leases); err != nil {
		return nil, fmt.Errorf("failed to decode leases: %v", err)
	}

	return leases, nil
}

// GetLeaseByID 根据ID获取租约
func GetLeaseByID(ctx context.Context, leaseID string) (*Lease, error) {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	var lease Lease
	err := leaseColl.FindOne(ctx, bson.M{"_id": leaseID}).Decode(&lease)
	if err != nil {
		return nil, fmt.Errorf("lease not found")
	}

	return &lease, nil
}

// UpdateFields 更新租约信息
func (l *Lease) UpdateFields(ctx context.Context, updates map[string]interface{}) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 添加访问控制检查
	scope, err := GetResourceScope(ctx, l.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = l.ID

	update := bson.M{
		"$set": updates,
	}

	result, err := leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update lease: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("lease not found or access denied")
	}

	return nil
}

// Delete 删除租约
func (l *Lease) Delete(ctx context.Context) error {
	// 首先检查删除约束
	if err := CheckLeaseDeletionConstraints(ctx, l.ID, l.UserID); err != nil {
		return err
	}

	// 1. 先删除所有文件
	if err := deleteLeaseFiles(ctx, l); err != nil {
		golog.Error("Failed to delete lease files", "error", err, "leaseId", l.ID)
		return fmt.Errorf("failed to delete lease files: %v", err)
	}

	// 2. 删除所有payment记录
	if err := deleteLeasePayments(ctx, l.ID, l.UserID); err != nil {
		golog.Error("Failed to delete lease payments", "error", err, "leaseId", l.ID)
		return fmt.Errorf("failed to delete lease payments: %v", err)
	}

	// 3. 最后删除lease记录
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 添加访问控制检查
	scope, err := GetResourceScope(ctx, l.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = l.ID

	result, err := leaseColl.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete lease: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("lease not found or access denied")
	}

	golog.Info("Successfully deleted lease and all related data", "leaseId", l.ID, "fileCount", len(l.Documents))
	return nil
}

// ClearDocument 清除租约的文档信息并关闭rent reporting
func (l *Lease) ClearDocument(ctx context.Context) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 获取原始租约数据以检查 rent reporting 状态
	var originalLease Lease
	err := leaseColl.FindOne(ctx, bson.M{"_id": l.ID}).Decode(&originalLease)
	if err != nil {
		return fmt.Errorf("failed to find original lease: %v", err)
	}

	update := bson.M{
		"$set": bson.M{
			"documents": []LeaseDocument{}, // 清空文档数组
			"rentRep":   false,             // 删除文件时自动关闭rent reporting
		},
	}

	_, err = leaseColl.UpdateOne(ctx, bson.M{"_id": l.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to clear document fields: %v", err)
	}

	// 清除本地对象的文档信息
	l.Documents = []LeaseDocument{}

	// 如果原来 rent reporting 是开启的，现在关闭了，发送暂停邮件
	if originalLease.RentReporting {
		l.RentReporting = false
		// 发送租金报告暂停通知邮件
		err = sendRentReportingPausedNotification(ctx, &originalLease, l)
		if err != nil {
			// 记录错误但不影响主要操作
			golog.Error("Failed to send rent reporting paused notification emails", "error", err, "leaseId", l.ID)
		}
	} else {
		l.RentReporting = false
	}

	return nil
}

// UpdateLeaseTenantAndSync 同时更新lease表ctnts和tenants表
// 使用MongoDB事务确保数据一致性
func UpdateLeaseTenantAndSync(ctx context.Context, leaseId, userId string, updatedTenant Tenant) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	var tenantColl = gomongo.Coll("rr", "tenants")
	if leaseColl == nil || tenantColl == nil {
		return fmt.Errorf("collections not initialized")
	}

	// 先在事务外进行所有的数据准备工作
	scope, err := GetResourceScope(ctx, userId)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}
	filter := scope.ToFilter()
	filter["_id"] = leaseId

	// 1. 查出原ctnts对象
	var lease Lease
	if err := leaseColl.FindOne(ctx, filter).Decode(&lease); err != nil {
		return fmt.Errorf("failed to find lease: %v", err)
	}

	// Scenario 5: Check if lease allows tenant modifications
	if lease.Status != "active" {
		return fmt.Errorf("cannot modify tenants for non-active lease (status: %s)", lease.Status)
	}
	var origCtnt *Tenant
	for i, t := range lease.CurrentTenants {
		if t.ID == updatedTenant.ID {
			origCtnt = &lease.CurrentTenants[i]
			break
		}
	}
	if origCtnt == nil {
		return fmt.Errorf("tenant not found in ctnts")
	}

	// 字段合并：只用前端非空字段覆盖原有字段
	mergeTenantFields(origCtnt, &updatedTenant)

	// 2. 查出原tenants对象
	filterTenant := bson.M{"_id": updatedTenant.ID, "leaseId": leaseId}
	var origTenant Tenant
	tenantColl.FindOne(ctx, filterTenant).Decode(&origTenant)
	mergeTenantFields(&origTenant, &updatedTenant)

	// 准备更新文档
	ctntUpdate, err := gomongo.ToBSONDoc(origCtnt)
	if err != nil {
		return fmt.Errorf("failed to create ctnts update doc: %v", err)
	}
	tenantUpdate, err := gomongo.ToBSONDoc(&origTenant)
	if err != nil {
		return fmt.Errorf("failed to create tenant update doc: %v", err)
	}

	// 为事务创建使用primary读取偏好的客户端
	// 从配置获取URI并修改为primary读取偏好
	uri, ok := goconfig.Config("dbs.rr.uri").(string)
	if !ok {
		return fmt.Errorf("failed to get database URI from config")
	}

	// 替换readPreference为primary（用于事务）
	transactionURI := strings.ReplaceAll(uri, "readPreference=nearest", "readPreference=primary")

	// 创建事务专用客户端
	transactionClient, err := mongo.Connect(ctx, options.Client().ApplyURI(transactionURI))
	if err != nil {
		return fmt.Errorf("failed to create transaction client: %v", err)
	}
	defer transactionClient.Disconnect(ctx)

	// 获取事务专用的collections
	transactionDB := transactionClient.Database("rr")
	transactionLeaseColl := transactionDB.Collection("leases")
	transactionTenantColl := transactionDB.Collection("tenants")

	// 开始会话和事务
	session, err := transactionClient.StartSession()
	if err != nil {
		return fmt.Errorf("failed to start session: %v", err)
	}
	defer session.EndSession(ctx)

	// 在事务中只执行写操作
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 3. 写回ctnts（在事务中）
		update := bson.M{
			"$set": bson.M{
				"ctnts.$[elem]": ctntUpdate,
			},
		}
		arrayFilters := options.ArrayFilters{
			Filters: []interface{}{bson.M{"elem._id": updatedTenant.ID}},
		}
		opts := options.Update().SetArrayFilters(arrayFilters)
		_, err := transactionLeaseColl.UpdateOne(sessCtx, filter, update, opts)
		if err != nil {
			return nil, fmt.Errorf("failed to update tenant in lease: %v", err)
		}

		// 4. 写回tenants表（在事务中）
		_, err = transactionTenantColl.UpdateOne(sessCtx, filterTenant, bson.M{"$set": tenantUpdate})
		if err != nil {
			return nil, fmt.Errorf("failed to update tenant: %v", err)
		}

		return nil, nil
	})

	if err != nil {
		return fmt.Errorf("transaction failed: %v", err)
	}

	return nil
}

// mergeTenantFields: 合并租户字段，必填字段只在非空时覆盖，可选字段始终覆盖（包括空值）
func mergeTenantFields(dst, src *Tenant) {
	// 必填字段：只在非空时覆盖
	if src.FirstName != "" {
		dst.FirstName = src.FirstName
	}
	if src.LastName != "" {
		dst.LastName = src.LastName
	}
	if src.Email != "" {
		dst.Email = src.Email
	}

	// 可选字段：始终覆盖，包括空值（允许用户清空这些字段）
	dst.MiddleName = src.MiddleName
	dst.Phone = src.Phone
	dst.SINNumber = src.SINNumber
	dst.Notes = src.Notes

	// DateOfBirth 特殊处理：如果src有值（包括nil），则覆盖
	dst.DateOfBirth = src.DateOfBirth

	// TenantId：只在非空时覆盖（避免意外清空绑定关系）
	if src.TenantId != "" {
		dst.TenantId = src.TenantId
	}
}

// calculateInitialOwingBalance 计算创建租约时的初始欠款余额
func calculateInitialOwingBalance(lease *Lease) float64 {
	// 解析开始日期
	startDate, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		// 如果日期解析失败，返回一个月的租金
		return lease.RentAmount + lease.AdditionalMonthlyFees
	}

	now := time.Now()
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 如果开始日期在未来，余额为0（还没开始）
	if startDate.After(now) {
		return 0.0
	}

	// 计算从开始日期到当前日期的月数（基于租金到期日）
	months := calculateMonthsBasedOnDueDay(startDate, now, lease.RentDueDay)

	// 至少计算1个月（如果租约已开始）
	if months < 1 {
		months = 1
	}

	totalBalance := float64(months) * monthlyRent

	return totalBalance
}

// calculateMonthsBasedOnDueDay 基于租金到期日计算应付月数
func calculateMonthsBasedOnDueDay(startDate, currentDate time.Time, rentDueDay int) int {
	// 确保 start 在 current 之前
	if startDate.After(currentDate) {
		return 0
	}

	// 如果没有设置到期日，默认为1号
	if rentDueDay < 1 || rentDueDay > 31 {
		rentDueDay = 1
	}

	months := 0

	// 从开始月份逐月检查到当前月份
	checkDate := time.Date(startDate.Year(), startDate.Month(), 1, 0, 0, 0, 0, startDate.Location())
	currentMonth := time.Date(currentDate.Year(), currentDate.Month(), 1, 0, 0, 0, 0, currentDate.Location())

	for !checkDate.After(currentMonth) {
		// 计算这个月的租金到期日
		dueDate := time.Date(checkDate.Year(), checkDate.Month(), rentDueDay, 0, 0, 0, 0, checkDate.Location())

		// 如果到期日超过了该月的最后一天，调整为该月最后一天
		lastDayOfMonth := time.Date(checkDate.Year(), checkDate.Month()+1, 0, 0, 0, 0, 0, checkDate.Location()).Day()
		if rentDueDay > lastDayOfMonth {
			dueDate = time.Date(checkDate.Year(), checkDate.Month(), lastDayOfMonth, 0, 0, 0, 0, checkDate.Location())
		}

		// 如果当前日期已经过了这个月的租金到期日，则计算这个月的租金
		if currentDate.After(dueDate) || currentDate.Equal(dueDate) {
			months++
		}

		// 移动到下一个月
		checkDate = checkDate.AddDate(0, 1, 0)
	}

	return months
}

// validateLeaseCanBeModified 验证租约是否可以被修改（Scenario 5）
func validateLeaseCanBeModified(ctx context.Context, leaseID string, newLease *Lease, scope *ResourceScope) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 获取当前租约信息
	filter := scope.ToFilter()
	filter["_id"] = leaseID

	var currentLease Lease
	err := leaseColl.FindOne(ctx, filter).Decode(&currentLease)
	if err != nil {
		return fmt.Errorf("failed to find current lease: %v", err)
	}

	// Scenario 5: 如果当前状态不是 active，只允许修改 status 字段
	if currentLease.Status != "active" {
		// 检查是否只修改了 status 字段
		if !isOnlyStatusChanged(&currentLease, newLease) {
			return fmt.Errorf("non-active lease (status: %s) can only modify status field", currentLease.Status)
		}
	}

	return nil
}

// isOnlyStatusChanged 检查是否只修改了 status 字段
func isOnlyStatusChanged(current, new *Lease) bool {
	// 比较关键字段是否相同（除了 status）
	return current.RentAmount == new.RentAmount &&
		current.AdditionalMonthlyFees == new.AdditionalMonthlyFees &&
		current.StartDate == new.StartDate &&
		current.EndDate == new.EndDate &&
		current.RentDueDay == new.RentDueDay &&
		current.PropertyID == new.PropertyID &&
		current.RoomID == new.RoomID &&
		current.AutoPay == new.AutoPay &&
		current.RentReporting == new.RentReporting
}

// handleRentAmountChange 处理租金变更的影响（Scenario 6）
func handleRentAmountChange(ctx context.Context, originalLease, newLease *Lease, scope *ResourceScope) error {
	// 检查租金是否发生变更
	oldMonthlyRent := originalLease.RentAmount + originalLease.AdditionalMonthlyFees
	newMonthlyRent := newLease.RentAmount + newLease.AdditionalMonthlyFees

	if oldMonthlyRent == newMonthlyRent {
		return nil // 租金没有变更，无需处理
	}

	// Scenario 6: 只影响当前时间之后的支付
	// 重新计算租约余额以反映租金变更
	err := recalculateLeaseBalanceAfterRentChange(ctx, newLease, oldMonthlyRent, newMonthlyRent, scope)
	if err != nil {
		return fmt.Errorf("failed to recalculate balance after rent change: %v", err)
	}

	return nil
}

// recalculateLeaseBalanceAfterRentChange 重新计算租金变更后的余额
func recalculateLeaseBalanceAfterRentChange(ctx context.Context, lease *Lease, oldMonthlyRent, newMonthlyRent float64, scope *ResourceScope) error {
	// 获取所有支付记录
	paymentColl := gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return fmt.Errorf("tenant_payments collection not initialized")
	}

	// 查询该租约的所有支付记录
	paymentFilter := scope.ToFilter()
	paymentFilter["leaseId"] = lease.ID

	cursor, err := paymentColl.Find(ctx, paymentFilter)
	if err != nil {
		return fmt.Errorf("failed to find payments: %v", err)
	}
	defer cursor.Close(ctx)

	var payments []TenantPayment
	if err = cursor.All(ctx, &payments); err != nil {
		return fmt.Errorf("failed to decode payments: %v", err)
	}

	// 重新计算余额：基于新租金标准
	// 1. 计算初始余额（基于新租金）
	newInitialBalance := calculateInitialOwingBalanceWithRent(lease, newMonthlyRent)

	// 2. 减去所有支付
	totalPayments := 0.0
	for _, payment := range payments {
		totalPayments += payment.Amount
	}

	// 3. 计算新余额（允许负数，表示多付款）
	newBalance := newInitialBalance - totalPayments

	// 4. 更新租约余额
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	filter := scope.ToFilter()
	filter["_id"] = lease.ID

	update := bson.M{
		"$set": bson.M{
			"owingBal": newBalance,
		},
	}

	_, err = leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	return nil
}

// calculateInitialOwingBalanceWithRent 使用指定租金计算初始余额
func calculateInitialOwingBalanceWithRent(lease *Lease, monthlyRent float64) float64 {
	// 解析开始日期
	startDate, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		// 如果日期解析失败，返回一个月的租金
		return monthlyRent
	}

	now := time.Now()

	// 如果开始日期在未来，余额为0（还没开始）
	if startDate.After(now) {
		return 0.0
	}

	// 计算从开始日期到当前日期的月数（基于租金到期日）
	months := calculateMonthsBasedOnDueDay(startDate, now, lease.RentDueDay)

	// 至少计算1个月（如果租约已开始）
	if months < 1 {
		months = 1
	}

	totalBalance := float64(months) * monthlyRent

	return totalBalance
}

// handleRentDueDayChange 处理租金到期日变更（Scenario 9）
func handleRentDueDayChange(ctx context.Context, originalLease, newLease *Lease) error {
	// 检查租金到期日是否发生变更
	if originalLease.RentDueDay == newLease.RentDueDay {
		return nil // 到期日没有变更，无需处理
	}

	// Scenario 9: RentDueDay 变更不影响历史余额，只影响未来的自动支付调度
	// 余额计算保持不变，这里只是记录变更日志

	// 验证新的到期日是否有效
	normalizedDueDay := normalizeRentDueDay(newLease.RentDueDay)
	if normalizedDueDay != newLease.RentDueDay {
		// 如果到期日被标准化了，更新租约
		newLease.RentDueDay = normalizedDueDay

		// 更新数据库中的到期日
		leaseColl := gomongo.Coll("rr", "leases")
		if leaseColl != nil {
			filter := bson.M{"_id": newLease.ID}
			update := bson.M{
				"$set": bson.M{
					"rentDueDay": normalizedDueDay,
				},
			}
			leaseColl.UpdateOne(ctx, filter, update)
		}
	}

	return nil
}

// normalizeRentDueDay 标准化租金到期日
func normalizeRentDueDay(rentDueDay int) int {
	// 处理边界情况
	if rentDueDay <= 0 {
		return 1 // 默认为1号
	}
	if rentDueDay > 31 {
		return 31 // 最大为31号
	}
	return rentDueDay
}

// ArchivePropertyLeases 批量更新指定property下的所有active leases
// 将status设为ended，rentRep和autoPay设为false
func ArchivePropertyLeases(ctx context.Context, propertyId, userId string) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// Get resource scope for access control
	scope, err := GetResourceScope(ctx, userId)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	// Build filter to find all active leases for this property
	filter := scope.ToFilter()
	filter["propId"] = propertyId
	filter["status"] = "active"

	// Update all matching leases
	update := bson.M{
		"$set": bson.M{
			"status":  "ended",
			"rentRep": false,
			"autoPay": false,
		},
	}

	result, err := leaseColl.UpdateMany(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to archive property leases: %v", err)
	}

	golog.Info("Archived leases for property", "modifiedCount", result.ModifiedCount, "propertyId", propertyId)
	return nil
}

// sendRentReportingEnabledNotification 发送租金报告启用通知
func sendRentReportingEnabledNotification(ctx context.Context, originalLease, updatedLease *Lease) error {
	// 获取房东信息（从当前登录用户的usrNm字段）
	user, err := GetUserByID(ctx, updatedLease.UserID)
	if err != nil {
		golog.Error("Failed to get landlord user info", "error", err, "userId", updatedLease.UserID)
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := GetProperty(ctx, updatedLease.PropertyID, updatedLease.UserID)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", updatedLease.PropertyID)
		// 不返回错误，继续发送邮件
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,   // 使用 username 作为名字
		LastName:        "",              // 没有单独的姓氏字段
		PropertyAddress: "your property", // 默认值
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	// 准备租户邮件信息
	var tenants []utils.TenantEmailInfo
	for _, tenant := range originalLease.CurrentTenants {
		if tenant.Email != "" && tenant.FirstName != "" {
			tenants = append(tenants, utils.TenantEmailInfo{
				Email:     tenant.Email,
				FirstName: tenant.FirstName,
			})
		}
	}

	if len(tenants) > 0 {
		// 异步发送邮件
		utils.SendRentReportingNotificationEmailsAsync(tenants, landlordInfo)
		golog.Info("Initiated rent reporting notification emails",
			"leaseId", updatedLease.ID,
			"tenantCount", len(tenants),
			"landlordName", landlordInfo.FirstName,
			"propertyAddress", landlordInfo.PropertyAddress,
			"propertyId", updatedLease.PropertyID)
	} else {
		golog.Warn("No valid tenant emails found for rent reporting notification", "leaseId", updatedLease.ID)
	}

	return nil
}

// sendRentReportingPausedNotification 发送租金报告暂停通知
func sendRentReportingPausedNotification(ctx context.Context, originalLease, updatedLease *Lease) error {
	// 获取房东信息（从当前登录用户的usrNm字段）
	user, err := GetUserByID(ctx, updatedLease.UserID)
	if err != nil {
		golog.Error("Failed to get landlord user info", "error", err, "userId", updatedLease.UserID)
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := GetProperty(ctx, updatedLease.PropertyID, updatedLease.UserID)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", updatedLease.PropertyID)
		// 不返回错误，继续发送邮件
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,   // 使用 username 作为名字
		LastName:        "",              // 没有单独的姓氏字段
		PropertyAddress: "your property", // 默认值
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	// 准备租户邮件信息
	var tenants []utils.TenantEmailInfo
	for _, tenant := range originalLease.CurrentTenants {
		if tenant.Email != "" && tenant.FirstName != "" {
			tenants = append(tenants, utils.TenantEmailInfo{
				Email:     tenant.Email,
				FirstName: tenant.FirstName,
			})
		}
	}

	if len(tenants) > 0 {
		// 异步发送暂停邮件
		utils.SendRentReportingPausedNotificationEmailsAsync(tenants, landlordInfo)
		golog.Info("Initiated rent reporting paused notification emails",
			"leaseId", updatedLease.ID,
			"tenantCount", len(tenants),
			"landlordName", landlordInfo.FirstName,
			"propertyAddress", landlordInfo.PropertyAddress,
			"propertyId", updatedLease.PropertyID)
	} else {
		golog.Warn("No valid tenant emails found for rent reporting paused notification", "leaseId", updatedLease.ID)
	}

	return nil
}

// handleSoftDeletedTenantRentReportingNotification 处理软删除租户时的租金报告暂停邮件通知
func handleSoftDeletedTenantRentReportingNotification(ctx context.Context, tenant *Tenant, lease *Lease) error {
	// 获取房东信息
	user, err := GetUserByID(ctx, lease.UserID)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := GetProperty(ctx, lease.PropertyID, lease.UserID)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", lease.PropertyID)
		// 不返回错误，继续发送邮件
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,   // 使用 username 作为名字
		LastName:        "",              // 没有单独的姓氏字段
		PropertyAddress: "your property", // 默认值
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	// 准备被软删除租户的邮件信息
	if tenant.Email != "" && tenant.FirstName != "" {
		tenants := []utils.TenantEmailInfo{
			{
				Email:     tenant.Email,
				FirstName: tenant.FirstName,
			},
		}

		// 异步发送暂停邮件给被软删除的租户
		utils.SendRentReportingPausedNotificationEmailsAsync(tenants, landlordInfo)
		golog.Info("Initiated rent reporting paused notification email for soft deleted tenant",
			"tenantId", tenant.ID,
			"leaseId", lease.ID,
			"tenantEmail", tenant.Email,
			"landlordName", landlordInfo.FirstName,
			"propertyAddress", landlordInfo.PropertyAddress,
			"propertyId", lease.PropertyID)
	} else {
		golog.Warn("Soft deleted tenant missing email or first name for rent reporting notification", "tenantId", tenant.ID, "leaseId", lease.ID)
	}

	return nil
}
