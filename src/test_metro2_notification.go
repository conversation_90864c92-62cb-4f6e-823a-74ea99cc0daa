package main

import (
	"context"
	"fmt"
	"rent_report/entities"
	"rent_report/services"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	fmt.Println("=== Metro2 Notification Test ===")

	// 初始化配置
	if err := goconfig.LoadConfig("local.ini"); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// 初始化日志
	golog.Init()

	// 初始化数据库连接
	if err := gomongo.Init(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	ctx := context.Background()

	// 测试1: 创建模拟的Metro2生成日志
	fmt.Println("\n1. Creating mock Metro2 generation log...")
	generationLog := createMockMetro2GenerationLog()
	
	err := generationLog.Create(ctx)
	if err != nil {
		fmt.Printf("Failed to create Metro2 generation log: %v\n", err)
		return
	}
	fmt.Printf("✅ Created Metro2 generation log: %s\n", generationLog.ID)

	// 测试2: 创建Metro2通知任务
	fmt.Println("\n2. Creating Metro2 notification task...")
	err = services.CreateMetro2NotificationTask(ctx, generationLog)
	if err != nil {
		fmt.Printf("Failed to create Metro2 notification task: %v\n", err)
		return
	}
	fmt.Println("✅ Metro2 notification task created successfully")

	// 测试3: 查看任务状态
	fmt.Println("\n3. Checking task status...")
	task, err := entities.GetMetro2NotificationTaskByGenerationID(ctx, generationLog.ID)
	if err != nil {
		fmt.Printf("Failed to get Metro2 notification task: %v\n", err)
		return
	}
	
	fmt.Printf("Task ID: %s\n", task.ID)
	fmt.Printf("Scheduled Time: %s\n", task.ScheduledTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("Completed: %t\n", task.Completed)
	fmt.Printf("Time until execution: %v\n", time.Until(task.ScheduledTime))

	// 测试4: 启动调度器（在后台运行）
	fmt.Println("\n4. Starting Metro2 notification scheduler...")
	go services.StartMetro2NotificationScheduler()

	// 测试5: 等待任务执行
	fmt.Println("\n5. Waiting for task execution...")
	fmt.Println("The scheduler will check for pending tasks every 1 minute.")
	fmt.Println("The notification email should be sent in about 1 minute.")
	fmt.Println("Press Ctrl+C to exit.")

	// 监控任务状态
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 检查任务状态
			task, err := entities.GetMetro2NotificationTaskByGenerationID(ctx, generationLog.ID)
			if err != nil {
				fmt.Printf("Error checking task status: %v\n", err)
				continue
			}

			if task.Completed {
				fmt.Printf("\n✅ Task completed!\n")
				fmt.Printf("Execution Time: %s\n", task.ExecutedAt.Format("2006-01-02 15:04:05"))
				fmt.Printf("Emails Sent: %d\n", task.EmailsSent)
				if task.ErrorMessage != "" {
					fmt.Printf("Error Message: %s\n", task.ErrorMessage)
				}
				return
			} else {
				timeUntil := time.Until(task.ScheduledTime)
				if timeUntil > 0 {
					fmt.Printf("⏳ Task pending. Time until execution: %v\n", timeUntil.Round(time.Second))
				} else {
					fmt.Printf("⏳ Task should be executing soon...\n")
				}
			}
		}
	}
}

// createMockMetro2GenerationLog 创建模拟的Metro2生成日志
func createMockMetro2GenerationLog() *entities.Metro2GenerationLog {
	now := time.Now()
	
	return &entities.Metro2GenerationLog{
		ID:          utils.GenerateNanoID(),
		UserID:      "test_user_" + utils.GenerateNanoID(),
		ReportMonth: now.Format("2006-01"),
		GeneratedAt: now,
		ProcessedLeases: []entities.Metro2LeaseInfo{
			{
				LeaseID:        "test_lease_" + utils.GenerateNanoID(),
				PropertyName:   "Test Property",
				TenantName:     "John Doe",
				TenantEmail:    "<EMAIL>",
				RentAmount:     1200.00,
				CurrentBalance: 150.00, // 有欠款，会触发邮件发送
				PaymentHistory: []entities.Metro2PaymentRecord{
					{
						Month:  now.AddDate(0, -1, 0).Format("2006-01"),
						Status: "30", // 30天逾期
						Amount: 1200.00,
					},
				},
			},
			{
				LeaseID:        "test_lease_2_" + utils.GenerateNanoID(),
				PropertyName:   "Test Property 2",
				TenantName:     "Jane Smith",
				TenantEmail:    "<EMAIL>",
				RentAmount:     1500.00,
				CurrentBalance: 0.00, // 无欠款，不会发送邮件
				PaymentHistory: []entities.Metro2PaymentRecord{
					{
						Month:  now.AddDate(0, -1, 0).Format("2006-01"),
						Status: "C1", // 按时付款
						Amount: 1500.00,
					},
				},
			},
		},
		TotalLeases:    2,
		TotalTenants:   2,
		FileSize:       1024,
		Status:         "completed",
		CompletedAt:    &now,
	}
}
