package utils

import (
	"fmt"
	"sync"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomail"
)

// getBaseURL 获取应用的基础URL
func getBaseURL() string {
	baseURL, ok := goconfig.Config("server.baseUrl").(string)
	if !ok || baseURL == "" {
		// 默认值，如果配置不存在
		return "http://localhost:8089"
	}
	return baseURL
}

// SendVerificationEmail sends a verification email to the user
func SendVerificationEmail(email, code string) error {
	baseUrl, ok := goconfig.Config("server.baseUrl").(string)
	if !ok || baseUrl == "" {
		baseUrl = "http://localhost:3000"
	}
	subject := "Verify your Report Rentals account to get started"
	htmlBody := fmt.Sprintf(`
		<p>Dear user,</p>
		<p>Thanks for signing up with Report Rentals! Please verify your email address to activate your account.</p>
		<p style="margin-top:16px;">Verification code: <strong>%s</strong></p>
		<p>If you didn't sign up for Report Rentals, you can ignore this email.</p>
		<p>—<br>The Report Rentals Team</p>
	`, code)

	// Get mailer instance
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		To:      []string{email},
		Subject: subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email using Gmail SMTP engine
	return mailer.SendMail("gmail", msg)
}

// SendResetPasswordEmail sends a reset password email to the user
func SendResetPasswordEmail(email, code string) error {
	baseUrl, ok := goconfig.Config("server.baseUrl").(string)
	if !ok || baseUrl == "" {
		baseUrl = "http://localhost:3000"
	}
	subject := "Reset Password Request"
	htmlBody := fmt.Sprintf(`
		<p>Dear user,</p>
		<p>We received the request to reset the password for your account.</p>
		<p>The code is valid for 24 hours.</p>
		<p style="margin-top:16px;">Verification code: <strong>%s</strong></p>
		<p>If you didn't request this, you can ignore this email.</p>
		<p>—<br>The Report Rentals Team</p>
	`, code)

	// Get mailer instance
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		To:      []string{email},
		Subject: subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email using Gmail SMTP engine
	return mailer.SendMail("gmail", msg)
}

// SendInvitationEmail sends an invitation email to the user
func SendInvitationEmail(senderName, receiverName, receiverEmail, code string) error {
	baseUrl, ok := goconfig.Config("server.baseUrl").(string)
	if !ok || baseUrl == "" {
		baseUrl = "http://localhost:3000"
	}
	invitationLink := fmt.Sprintf("%s/pages/invitation_verify/?code=%s", baseUrl, code)
	subject := fmt.Sprintf("%s has invited you to join Report Rentals", senderName)
	htmlBody := fmt.Sprintf(`
		<p>Hi %s,</p>
		<p>%s has invited you to join the Report Rentals, a platform that helps tenants and landlords track and report rental history.</p>
		<p>To accept the invitation and set up your account, click below:</p>
		<p><a href="%s" style="display:inline-block;padding:10px 24px;background:#2563eb;color:#fff;text-decoration:none;border-radius:6px;font-weight:bold;">Accept Invitation</a></p>
		<p>If you weren't expecting this invitation, feel free to ignore it.</p>
		<p>—<br>The Report Rentals Team</p>
	`, receiverName, senderName, invitationLink)

	// Get mailer instance
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		To:      []string{receiverEmail},
		Subject: subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email using Gmail SMTP engine
	return mailer.SendMail("gmail", msg)
}

// SendRentReportingNotificationEmail 发送租金报告启用通知邮件
func SendRentReportingNotificationEmail(tenantEmail, tenantFirstName, landlordFirstName, landlordLastName, propertyAddress string) error {
	subject := "Rent reporting has been enabled for your lease"

	// 构建房东全名
	landlordFullName := fmt.Sprintf("%s %s", landlordFirstName, landlordLastName)
	if landlordLastName == "" {
		landlordFullName = landlordFirstName
	}

	// 构建登录链接 - 需要从环境变量或配置中获取 base URL
	baseURL := getBaseURL()
	signInURL := fmt.Sprintf("%s/pages/signup/", baseURL)

	htmlBody := fmt.Sprintf(`
		<p>Hi %s,</p>

		<p>Your landlord %s at %s has enabled rent reporting for your lease. Your monthly rent can now contribute to your credit score.</p>

		<p>Sign in to view your lease — or sign up if you don't have an account.</p>

		<p><a href="%s" style="color: #000000;">Sign in</a></p>

		<p>The Report Rentals Team</p>
	`, tenantFirstName, landlordFullName, propertyAddress, signInURL)

	// Get mailer instance
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		To:      []string{tenantEmail},
		Subject: subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email using Gmail SMTP engine
	return mailer.SendMail("gmail", msg)
}

// TenantEmailInfo 租户邮件信息结构
type TenantEmailInfo struct {
	Email     string
	FirstName string
}

// LandlordInfo 房东信息结构
type LandlordInfo struct {
	FirstName       string
	LastName        string
	PropertyAddress string
}

// SendRentReportingNotificationEmailsAsync 异步发送租金报告启用通知邮件给多个租户
func SendRentReportingNotificationEmailsAsync(tenants []TenantEmailInfo, landlordInfo LandlordInfo) {
	if len(tenants) == 0 {
		return
	}

	// 使用 goroutine 异步发送邮件
	go func() {
		var wg sync.WaitGroup

		// 限制并发数量，避免过多的并发邮件发送
		semaphore := make(chan struct{}, 5) // 最多5个并发邮件发送

		for _, tenant := range tenants {
			wg.Add(1)
			go func(t TenantEmailInfo) {
				defer wg.Done()

				// 获取信号量
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				// 发送邮件
				err := SendRentReportingNotificationEmail(t.Email, t.FirstName, landlordInfo.FirstName, landlordInfo.LastName, landlordInfo.PropertyAddress)
				if err != nil {
					// 记录错误但不影响其他邮件发送
					golog.Error("Failed to send rent reporting notification email", "firstName", t.FirstName, "email", t.Email, "error", err)
				} else {
					golog.Info("Successfully sent rent reporting notification email", "firstName", t.FirstName, "email", t.Email)
				}
			}(tenant)
		}

		// 等待所有邮件发送完成
		wg.Wait()
		golog.Info("Completed sending rent reporting notification emails",
			"tenantCount", len(tenants),
			"landlordFirstName", landlordInfo.FirstName,
			"landlordLastName", landlordInfo.LastName,
			"propertyAddress", landlordInfo.PropertyAddress)
	}()
}

// SendInvoiceEmail 发送订阅账单邮件
func SendInvoiceEmail(email, name, renewalDate, rentReports, total string) error {
	subject := fmt.Sprintf("Your subscription renews on %s", renewalDate)
	htmlBody := fmt.Sprintf(`
		<h2>Your subscription renews on %s</h2>
		<p>Hi there,</p>
		<p>Your Professional monthly subscription is about to renew soon.</p>
		<p><b>Account:</b> %s<br/>
		<b>Renewal date:</b> %s<br/>
		<b>Rent reports:</b> %s<br/>
		<b>Total:</b> %s</p>
		<p>If you have any additional questions or concerns, please send us a message.</p>
		<p>-<br/>The Report Rentals team,</p>
		<p><a href="mailto:<EMAIL>"><EMAIL></a></p>
	`, renewalDate, name, renewalDate, rentReports, total)

	// Get mailer instance
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		To:      []string{email},
		Subject: subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email using Gmail SMTP engine
	return mailer.SendMail("gmail", msg)
}

// SendRentReportingPausedNotificationEmail sends email notification when rent reporting is paused
func SendRentReportingPausedNotificationEmail(tenantEmail, tenantFirstName, landlordFullName, propertyAddress string) error {
	signInURL := fmt.Sprintf("%s/pages/signup/", getBaseURL())

	subject := "Rent reporting has been paused"

	htmlBody := fmt.Sprintf(`
		<p>Hi %s,</p>

		<p>Your landlord %s at %s has paused rent reporting for your lease.</p>

		<p>Sign in to view your lease — or sign up if you don't have an account.</p>

		<p><a href="%s" style="color: #000000;">Sign in</a></p>

		<p>The Report Rentals Team</p>
	`, tenantFirstName, landlordFullName, propertyAddress, signInURL)

	// Get mailer instance
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		To:      []string{tenantEmail},
		Subject: subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email using Gmail SMTP engine
	return mailer.SendMail("gmail", msg)
}

// SendMetro2ReportedNotificationEmail sends email notification when rent has been reported to credit bureaus
func SendMetro2ReportedNotificationEmail(tenantEmail, tenantFirstName, landlordFullName, propertyAddress, reportMonth, rentAmount, remainingBalance string) error {
	signInURL := fmt.Sprintf("%s/pages/signup/", getBaseURL())

	subject := "Your Rental Payment History Has Been Reported to Equifax"

	htmlBody := fmt.Sprintf(`
		<p>Hi %s,</p>

		<p>The following information has been submitted to Equifax this month. Please review the following information.</p>

		<p><strong>Monthly Rent:</strong> $%s<br/>
		<strong>Outstanding Balance:</strong> %s<br/>
		<strong>Landlord:</strong> %s</p>

		<p>If you believe this was reported in error or you have questions about your account, please contact <NAME_EMAIL> within 7 days.</p>

		<p><a href="%s" style="color: #000000;">View Lease Info</a></p>

		<p>—<br/>
		The Report Rentals Team</p>
	`, tenantFirstName, rentAmount, remainingBalance, landlordFullName, signInURL)

	// Get mailer instance
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		To:      []string{tenantEmail},
		Subject: subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email using Gmail SMTP engine
	return mailer.SendMail("gmail", msg)
}

// Metro2TenantEmailInfo Metro2租户邮件信息结构（包含租金和余额信息）
type Metro2TenantEmailInfo struct {
	Email            string
	FirstName        string
	RentAmount       string
	RemainingBalance string
}

// SendMetro2ReportedNotificationEmailsAsync 异步发送Metro2上报通知邮件给多个租户
func SendMetro2ReportedNotificationEmailsAsync(tenants []Metro2TenantEmailInfo, landlordInfo LandlordInfo, reportMonth string) {
	if len(tenants) == 0 {
		return
	}

	// 使用 goroutine 异步发送邮件
	go func() {
		var wg sync.WaitGroup

		// 限制并发数量，避免过多的并发邮件发送
		semaphore := make(chan struct{}, 5) // 最多5个并发邮件发送

		for _, tenant := range tenants {
			wg.Add(1)
			go func(t Metro2TenantEmailInfo) {
				defer wg.Done()

				// 获取信号量
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				// 发送邮件
				landlordFullName := fmt.Sprintf("%s %s", landlordInfo.FirstName, landlordInfo.LastName)
				err := SendMetro2ReportedNotificationEmail(t.Email, t.FirstName, landlordFullName, landlordInfo.PropertyAddress, reportMonth, t.RentAmount, t.RemainingBalance)
				if err != nil {
					// 记录错误但不影响其他邮件发送
					golog.Error("Failed to send Metro2 reported notification email", "firstName", t.FirstName, "email", t.Email, "reportMonth", reportMonth, "error", err)
				} else {
					golog.Info("Successfully sent Metro2 reported notification email", "firstName", t.FirstName, "email", t.Email, "reportMonth", reportMonth)
				}
			}(tenant)
		}

		// 等待所有邮件发送完成
		wg.Wait()
		golog.Info("Completed sending Metro2 reported notification emails", "tenantCount", len(tenants), "reportMonth", reportMonth)
	}()
}

// SendRentReportingPausedNotificationEmailsAsync 异步发送租金报告暂停通知邮件给多个租户
func SendRentReportingPausedNotificationEmailsAsync(tenants []TenantEmailInfo, landlordInfo LandlordInfo) {
	if len(tenants) == 0 {
		return
	}

	// 使用 goroutine 异步发送邮件
	go func() {
		landlordFullName := fmt.Sprintf("%s %s", landlordInfo.FirstName, landlordInfo.LastName)

		for _, tenant := range tenants {
			if err := SendRentReportingPausedNotificationEmail(tenant.Email, tenant.FirstName, landlordFullName, landlordInfo.PropertyAddress); err != nil {
				golog.Error("Failed to send rent reporting paused notification email", "email", tenant.Email, "error", err)
			} else {
				golog.Info("Successfully sent rent reporting paused notification email", "email", tenant.Email, "firstName", tenant.FirstName)
			}
		}
	}()
}

// DebtTenantEmailInfo 欠款租户邮件信息结构
type DebtTenantEmailInfo struct {
	Email           string
	FirstName       string
	RentAmount      string
	DueDate         string
	PropertyAddress string
}

// SendDebtReminderEmail sends email notification for unpaid rent with retry mechanism
func SendDebtReminderEmail(tenantEmail, tenantFirstName, landlordFullName, propertyAddress, rentAmount, dueDate string) error {
	signInURL := fmt.Sprintf("%s/pages/signup/", getBaseURL())

	subject := "Unpaid rent reminder"

	htmlBody := fmt.Sprintf(`
		<p>Hi %s,</p>

		<p>Just a reminder that your rent is due on %s for the property at %s.</p>

		<p><strong>Amount Due:</strong> $%s<br/>
		<strong>Landlord:</strong> %s</p>

		<p>Want to keep your payment history strong? Make sure to pay on time so your rent reporting stays in good standing.</p>

		<p><a href="%s" style="color: #000000;">View Lease Info</a></p>

		<p>—<br/>
		The Report Rentals Team</p>
	`, tenantFirstName, dueDate, propertyAddress, rentAmount, landlordFullName, signInURL)

	// Create email message
	msg := &gomail.EmailMessage{
		To:      []string{tenantEmail},
		Subject: subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email with retry mechanism
	return sendEmailWithRetry(msg, tenantEmail, tenantFirstName)
}

// sendEmailWithRetry 带重试机制的邮件发送函数
func sendEmailWithRetry(msg *gomail.EmailMessage, tenantEmail, tenantFirstName string) error {
	maxRetries := 3
	baseDelay := time.Second * 2

	var lastErr error
	for attempt := 1; attempt <= maxRetries; attempt++ {
		// Get mailer instance for each attempt (in case connection was closed)
		mailer, err := gomail.GetSendMailObj()
		if err != nil {
			lastErr = fmt.Errorf("failed to get mailer: %v", err)
			golog.Warn("Failed to get mailer instance",
				"attempt", attempt,
				"email", tenantEmail,
				"firstName", tenantFirstName,
				"error", err)

			if attempt < maxRetries {
				delay := time.Duration(attempt) * baseDelay
				golog.Info("Retrying after delay",
					"attempt", attempt,
					"delay", delay,
					"email", tenantEmail)
				time.Sleep(delay)
				continue
			}
			break
		}

		// Attempt to send email
		err = mailer.SendMail("gmail", msg)
		if err == nil {
			// Success
			return nil
		}

		lastErr = err
		golog.Warn("Failed to send email",
			"attempt", attempt,
			"maxRetries", maxRetries,
			"email", tenantEmail,
			"firstName", tenantFirstName,
			"error", err)

		// If this is not the last attempt, wait before retrying
		if attempt < maxRetries {
			delay := time.Duration(attempt) * baseDelay
			golog.Info("Retrying email send after delay",
				"attempt", attempt,
				"delay", delay,
				"email", tenantEmail)
			time.Sleep(delay)
		}
	}

	// All attempts failed
	return fmt.Errorf("failed to send email after %d attempts: %v", maxRetries, lastErr)
}

// SendDebtReminderEmailsAsync 异步发送欠款提醒邮件给多个租户
func SendDebtReminderEmailsAsync(tenants []DebtTenantEmailInfo, landlordInfo LandlordInfo) {
	if len(tenants) == 0 {
		golog.Info("📭 No tenants to send debt reminder emails to")
		fmt.Printf("📭 No tenants to send debt reminder emails to\n")
		return
	}

	landlordFullName := fmt.Sprintf("%s %s", landlordInfo.FirstName, landlordInfo.LastName)
	golog.Info("🚀 Starting async debt reminder email sending",
		"tenantCount", len(tenants),
		"landlordName", landlordFullName)
	fmt.Printf("🚀 Starting to send %d debt reminder emails for landlord: %s\n", len(tenants), landlordFullName)

	// 使用 goroutine 异步发送邮件
	go func() {
		var wg sync.WaitGroup
		successCount := 0
		errorCount := 0

		// 限制并发数量，避免过多的并发邮件发送
		semaphore := make(chan struct{}, 5) // 最多5个并发邮件发送

		for i, tenant := range tenants {
			wg.Add(1)
			go func(index int, t DebtTenantEmailInfo) {
				defer wg.Done()

				// 获取信号量
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				golog.Info("📧 Sending debt reminder email",
					"index", index+1,
					"firstName", t.FirstName,
					"email", t.Email,
					"amount", t.RentAmount,
					"dueDate", t.DueDate)
				fmt.Printf("📧 Sending email %d/%d to %s (%s) - $%s due on %s\n",
					index+1, len(tenants), t.FirstName, t.Email, t.RentAmount, t.DueDate)

				// 发送邮件
				err := SendDebtReminderEmail(t.Email, t.FirstName, landlordFullName, t.PropertyAddress, t.RentAmount, t.DueDate)
				if err != nil {
					// 记录错误但不影响其他邮件发送
					golog.Error("❌ Failed to send debt reminder email", "firstName", t.FirstName, "email", t.Email, "error", err)
					fmt.Printf("❌ Failed to send email to %s: %v\n", t.Email, err)
					errorCount++
				} else {
					golog.Info("✅ Successfully sent debt reminder email", "firstName", t.FirstName, "email", t.Email)
					fmt.Printf("✅ Successfully sent email to %s (%s)\n", t.FirstName, t.Email)
					successCount++
				}
			}(i, tenant)
		}

		// 等待所有邮件发送完成
		wg.Wait()
		golog.Info("🎉 Completed sending debt reminder emails",
			"tenantCount", len(tenants),
			"successCount", successCount,
			"errorCount", errorCount,
			"landlordName", landlordFullName)
		fmt.Printf("🎉 Completed sending debt reminder emails for %s: %d success, %d errors\n",
			landlordFullName, successCount, errorCount)
	}()
}
