package utils

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// UpdateRentReportEmailQueue 更新租金报告邮件队列
// 这是所有租户状态变化的统一入口点
func UpdateRentReportEmailQueue(ctx context.Context, tenantId, tenantEmail, tenantFirstName, leaseId, propId, landlordId, finalStatus string) {
	err := upsertRentReportEmailQueue(ctx, tenantId, tenantEmail, tenantFirstName, leaseId, propId, landlordId, finalStatus)
	if err != nil {
		golog.Error("Failed to update rent report email queue",
			"error", err,
			"tenantId", tenantId,
			"tenantEmail", tenantEmail,
			"finalStatus", finalStatus)
		return
	}

	golog.Info("Updated rent report email queue",
		"tenantId", tenantId,
		"tenantEmail", tenantEmail,
		"leaseId", leaseId,
		"finalStatus", finalStatus)
}

// upsertRentReportEmailQueue 内部函数，避免循环导入
func upsertRentReportEmailQueue(ctx context.Context, tenantId, tenantEmail, tenantFirstName, leaseId, propId, landlordId, finalStatus string) error {
	coll := gomongo.Coll("rr", "rent_report_email_queue")
	if coll == nil {
		return fmt.Errorf("rent_report_email_queue collection not initialized")
	}

	now := time.Now()
	filter := bson.M{
		"tntId":     tenantId,
		"processed": false, // 只更新未处理的记录
	}

	// 检查是否已存在记录
	var existingRecord bson.M
	err := coll.FindOne(ctx, filter).Decode(&existingRecord)

	var update bson.M
	if err != nil {
		// 记录不存在，创建新记录
		// 获取租户当前的实际状态作为初始状态
		currentStatus, statusErr := getTenantCurrentStatus(ctx, tenantId, leaseId)
		if statusErr != nil {
			// 如果无法获取当前状态，使用与最终状态相反的状态作为初始状态
			if finalStatus == "active" {
				currentStatus = "inactive"
			} else {
				currentStatus = "active"
			}
		}

		update = bson.M{
			"$set": bson.M{
				"tntEmail":      tenantEmail,
				"tntFirstName":  tenantFirstName,
				"leaseId":       leaseId,
				"propId":        propId,
				"landlordId":    landlordId,
				"initialStatus": currentStatus, // 使用当前实际状态作为初始状态
				"finalStatus":   finalStatus,
				"lastUpdated":   now,
			},
			"$setOnInsert": bson.M{
				"_id":       tenantId + "_" + fmt.Sprintf("%d", now.Unix()), // 使用租户ID+时间戳作为唯一ID
				"createdAt": now,
				"processed": false,
			},
		}
	} else {
		// 记录已存在，只更新最终状态，保持初始状态不变
		update = bson.M{
			"$set": bson.M{
				"tntEmail":     tenantEmail,
				"tntFirstName": tenantFirstName,
				"leaseId":      leaseId,
				"propId":       propId,
				"landlordId":   landlordId,
				"finalStatus":  finalStatus, // 只更新最终状态
				"lastUpdated":  now,
			},
		}
	}

	opts := options.Update().SetUpsert(true)
	_, err = coll.UpdateOne(ctx, filter, update, opts)
	return err
}

// getTenantCurrentStatus 获取租户当前的实际状态
func getTenantCurrentStatus(ctx context.Context, tenantId, leaseId string) (string, error) {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return "", fmt.Errorf("leases collection not initialized")
	}

	// 查询租约信息
	var lease struct {
		RentReporting  bool `bson:"rentRep"`
		CurrentTenants []struct {
			ID string `bson:"_id"`
		} `bson:"ctnts"`
	}

	err := leaseColl.FindOne(ctx, bson.M{"_id": leaseId}).Decode(&lease)
	if err != nil {
		return "", fmt.Errorf("failed to get lease: %v", err)
	}

	// 检查是否在当前租户列表中
	for _, tenant := range lease.CurrentTenants {
		if tenant.ID == tenantId {
			if lease.RentReporting {
				return "active", nil
			} else {
				return "inactive", nil
			}
		}
	}

	// 不在当前租户列表中，状态为inactive
	return "inactive", nil
}
