package main

import (
	"fmt"
	"rent_report/services"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	fmt.Println("=== Simple Debt Reminder Test ===")
	fmt.Println()

	// 初始化配置
	fmt.Println("🔧 Loading configuration...")
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("❌ Failed to load config: %v\n", err)
		return
	}
	fmt.Println("✅ Configuration loaded")

	// 初始化日志
	fmt.Println("📝 Initializing logging...")
	golog.Init("debug")
	fmt.Println("✅ Logging initialized")

	// 初始化数据库连接
	fmt.Println("🗄️ Connecting to database...")
	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("❌ Failed to initialize MongoDB: %v\n", err)
		return
	}
	fmt.Println("✅ Database connected")

	// 启动欠款提醒调度器
	fmt.Println()
	fmt.Println("🚀 Starting debt reminder scheduler...")
	services.StartDebtReminderScheduler()

	// 等待一段时间让调度器执行
	fmt.Println()
	fmt.Println("⏳ Waiting for scheduler to process...")
	fmt.Println("   (In test mode, emails should be sent immediately)")
	fmt.Println()

	// 等待10秒钟观察输出
	for i := 10; i > 0; i-- {
		fmt.Printf("⏰ Waiting %d seconds...\r", i)
		time.Sleep(1 * time.Second)
	}

	fmt.Println()
	fmt.Println("✅ Test completed. Check the output above for results.")
	fmt.Println()
	fmt.Println("If you see:")
	fmt.Println("  📭 'No tenants with debt found' - No leases with outstanding balance")
	fmt.Println("  📧 'Sending X emails' - Found tenants and sending emails")
	fmt.Println("  ❌ 'Failed to...' - Check database connection or data")
}
