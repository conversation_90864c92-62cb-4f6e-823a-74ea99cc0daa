2025-07-31T10:30:56.504-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-07-31T10:30:56.519-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-07-31T10:30:56.527-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-07-31T10:30:56.529-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-07-31T10:30:56.531-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-07-31T10:30:56.532-04:00 level=INFO msg=🚀 Starting debt reminder scheduler..., sendDay=15, testMode=true, processIntervalMinutes=60, cleanupDays=30
2025-07-31T10:30:56.533-04:00 level=INFO msg=🚀 Debt reminder processing worker started, intervalMinutes=60, testMode=true, sendDay=15
2025-07-31T10:30:56.534-04:00 level=INFO, value="🧪 TEST MODE: Sending debt reminder emails immediately"
2025-07-31T10:30:56.536-04:00 level=INFO, value="📋 Processing debt reminder task"
2025-07-31T10:30:56.536-04:00 level=INFO, value="🔍 Searching for tenants with debt..."
2025-07-31T10:30:56.537-04:00 level=INFO, value="🔍 Querying active leases with debt..."
2025-07-31T10:30:56.54-04:00 level=INFO msg=📊 Found leases with debt, count=0
2025-07-31T10:30:56.541-04:00 level=INFO, value="📭 No tenants with debt found for reminder emails"
2025-07-31T10:34:03.149-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-07-31T10:34:03.154-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-07-31T10:34:03.158-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-07-31T10:34:03.159-04:00 level=INFO msg=Registered predefined collection, collName="mail_log", collection="mailLog", database="rr"
2025-07-31T10:34:03.161-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-07-31T10:34:03.162-04:00 level=INFO msg=🚀 Starting debt reminder scheduler..., sendDay=15, testMode=true, processIntervalMinutes=60, cleanupDays=30
2025-07-31T10:34:03.164-04:00 level=INFO msg=🚀 Debt reminder processing worker started, sendDay=15, intervalMinutes=60, testMode=true
2025-07-31T10:34:03.165-04:00 level=INFO, value="🧪 TEST MODE: Sending debt reminder emails immediately"
2025-07-31T10:34:03.165-04:00 level=INFO, value="📋 Processing debt reminder task"
2025-07-31T10:34:03.166-04:00 level=INFO, value="🔍 Searching for tenants with debt..."
2025-07-31T10:34:03.167-04:00 level=INFO, value="🔍 Querying active leases with debt..."
2025-07-31T10:34:03.172-04:00 level=ERROR msg=❌ Failed to query leases, error={}, file="debt_reminder_scheduler.go:211"
2025-07-31T10:34:03.172-04:00 level=ERROR msg=❌ Failed to get tenants with debt, error={}, file="debt_reminder_scheduler.go:159"
2025-07-31T10:35:40.21-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-07-31T10:35:40.214-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-07-31T10:35:40.218-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-07-31T10:35:40.219-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-07-31T10:35:40.226-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-07-31T10:35:40.226-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-07-31T10:35:40.228-04:00 level=INFO msg=🚀 Starting debt reminder scheduler..., sendDay=15, testMode=true, processIntervalMinutes=60, cleanupDays=30
2025-07-31T10:35:40.228-04:00 level=INFO msg=🚀 Debt reminder processing worker started, intervalMinutes=60, testMode=true, sendDay=15
2025-07-31T10:35:40.229-04:00 level=INFO, value="🧪 TEST MODE: Sending debt reminder emails immediately"
2025-07-31T10:35:40.231-04:00 level=INFO, value="📋 Processing debt reminder task"
2025-07-31T10:35:40.232-04:00 level=INFO, value="🔍 Searching for tenants with debt..."
2025-07-31T10:35:40.233-04:00 level=INFO, value="🔍 Querying active leases with debt..."
2025-07-31T10:35:40.236-04:00 level=INFO msg=📊 Found leases with debt, count=8
2025-07-31T10:35:40.242-04:00 level=INFO msg=✅ Found tenants with debt, landlordCount=2
2025-07-31T10:35:40.243-04:00 level=INFO msg=📧 Sending debt reminder emails, landlordId="6RDHxuNP6pT", landlordName="metro2 first name metro2 last name", tenantCount=12
2025-07-31T10:35:40.243-04:00 level=INFO msg=🚀 Starting async debt reminder email sending, tenantCount=12, landlordName="metro2 first name metro2 last name"
2025-07-31T10:35:40.244-04:00 level=INFO msg=📧 Sending debt reminder emails, landlordId="ZbqaKSetPJE", landlordName="test1 test1", tenantCount=2
2025-07-31T10:35:40.244-04:00 level=INFO msg=📧 Sending debt reminder email, email="<EMAIL>", amount=9000, dueDate="August 1, 2025", index=12, firstName="Jacob"
2025-07-31T10:35:40.244-04:00 level=INFO msg=📧 Sending debt reminder email, index=10, firstName="Lily", email="<EMAIL>", amount=9000, dueDate="August 1, 2025"
2025-07-31T10:35:40.254-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.244-04:00 level=INFO msg=📧 Sending debt reminder email, amount=6000, dueDate="August 1, 2025", index=6, firstName="Olivia", email="<EMAIL>"
2025-07-31T10:35:40.244-04:00 level=INFO msg=📧 Sending debt reminder email, firstName="Mia", email="<EMAIL>", amount=8000, dueDate="August 1, 2025", index=7
2025-07-31T10:35:40.245-04:00 level=INFO msg=🚀 Starting async debt reminder email sending, tenantCount=2, landlordName="test1 test1"
2025-07-31T10:35:40.258-04:00 level=INFO msg=🎉 Completed debt reminder task, totalEmailsSent=14
2025-07-31T10:35:40.244-04:00 level=INFO msg=📧 Sending debt reminder email, index=9, firstName="Amelia", email="<EMAIL>", amount=8000, dueDate="August 1, 2025"
2025-07-31T10:35:40.259-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.26-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.257-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.261-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.261-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.258-04:00 level=INFO msg=📧 Sending debt reminder email, index=1, firstName="xiaoou", email="<EMAIL>", amount=1000, dueDate="August 1, 2025"
2025-07-31T10:35:40.262-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.263-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.264-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.252-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.265-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.266-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.255-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.267-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.26-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.265-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, file="baseMailEngine.go:39", sesH="<EMAIL>"
2025-07-31T10:35:40.258-04:00 level=INFO msg=📧 Sending debt reminder email, amount=1000, dueDate="August 1, 2025", index=2, firstName="xiaoou", email="<EMAIL>"
2025-07-31T10:35:40.262-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.267-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.271-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.268-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.269-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.269-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.275-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-07-31T10:35:40.271-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.255-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.272-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-07-31T10:35:40.273-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.274-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.287-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-07-31T10:35:40.28-04:00 level=WARN msg=Warning: email name is too short:, email="<EMAIL>", file="mail_utils.go:187"
2025-07-31T10:35:40.292-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:321"
2025-07-31T10:35:40.293-04:00 level=INFO msg=✅ Successfully sent debt reminder email, firstName="Mia", email="<EMAIL>"
2025-07-31T10:35:40.27-04:00 level=WARN, file="mail_utils.go:50", value="defaultEmail not found in config"
2025-07-31T10:35:40.282-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.285-04:00 level=WARN msg=Warning: email name is too short:, email="<EMAIL>", file="mail_utils.go:187"
2025-07-31T10:35:40.294-04:00 level=INFO msg=📧 Sending debt reminder email, index=3, firstName="Benjamin", email="<EMAIL>", amount=5000, dueDate="August 1, 2025"
2025-07-31T10:35:40.3-04:00 level=WARN, file="mail_utils.go:50", value="defaultEmail not found in config"
2025-07-31T10:35:40.296-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.299-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:321"
2025-07-31T10:35:40.295-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.301-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.304-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, file="baseMailEngine.go:39", ses="<EMAIL>"
2025-07-31T10:35:40.303-04:00 level=INFO msg=✅ Successfully sent debt reminder email, email="<EMAIL>", firstName="Lily"
2025-07-31T10:35:40.304-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.302-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.305-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.307-04:00 level=INFO msg=📧 Sending debt reminder email, dueDate="August 1, 2025", index=1, firstName="James", email="<EMAIL>", amount=2000
2025-07-31T10:35:40.31-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.314-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.312-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.313-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.318-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.315-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-07-31T10:35:40.316-04:00 level=WARN msg=Warning: email name is too short:, email="<EMAIL>", file="mail_utils.go:187"
2025-07-31T10:35:40.311-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.322-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.326-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:321"
2025-07-31T10:35:40.329-04:00 level=INFO msg=✅ Successfully sent debt reminder email, firstName="Benjamin", email="<EMAIL>"
2025-07-31T10:35:40.328-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.328-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-07-31T10:35:40.33-04:00 level=INFO msg=📧 Sending debt reminder email, index=2, firstName="Sophia", email="<EMAIL>", amount=3000, dueDate="August 1, 2025"
2025-07-31T10:35:40.33-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.331-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.333-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.332-04:00 level=WARN msg=Warning: email name is too short:, email="<EMAIL>", file="mail_utils.go:187"
2025-07-31T10:35:40.334-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, file="baseMailEngine.go:39", ses="<EMAIL>"
2025-07-31T10:35:40.334-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:321"
2025-07-31T10:35:40.335-04:00 level=INFO msg=✅ Successfully sent debt reminder email, firstName="James", email="<EMAIL>"
2025-07-31T10:35:40.335-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.336-04:00 level=INFO msg=📧 Sending debt reminder email, amount=8000, dueDate="August 1, 2025", index=8, firstName="Ethan", email="<EMAIL>"
2025-07-31T10:35:40.338-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.34-04:00 level=WARN msg=Warning: email name is too short:, email="<EMAIL>", file="mail_utils.go:187"
2025-07-31T10:35:40.341-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:321"
2025-07-31T10:35:40.342-04:00 level=INFO msg=✅ Successfully sent debt reminder email, firstName="Sophia", email="<EMAIL>"
2025-07-31T10:35:40.339-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.343-04:00 level=INFO msg=📧 Sending debt reminder email, firstName="Daniel", email="<EMAIL>", amount=6000, dueDate="August 1, 2025", index=5
2025-07-31T10:35:40.344-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.346-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.347-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.346-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.347-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, file="baseMailEngine.go:39", ses="<EMAIL>"
2025-07-31T10:35:40.348-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.348-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.349-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.349-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.35-04:00 level=WARN msg=Warning: email name is too short:, file="mail_utils.go:187", email="<EMAIL>"
2025-07-31T10:35:40.351-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:321"
2025-07-31T10:35:40.351-04:00 level=INFO msg=✅ Successfully sent debt reminder email, firstName="Daniel", email="<EMAIL>"
2025-07-31T10:35:40.35-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-07-31T10:35:40.352-04:00 level=INFO msg=📧 Sending debt reminder email, index=11, firstName="Noah", email="<EMAIL>", amount=9000, dueDate="August 1, 2025"
2025-07-31T10:35:40.353-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.353-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.354-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.354-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, sesH="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.355-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.355-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-07-31T10:35:40.817-04:00 level=INFO msg=✅ Successfully sent debt reminder email, firstName="xiaoou", email="<EMAIL>"
2025-07-31T10:35:40.819-04:00 level=ERROR msg=❌ Failed to send debt reminder email, firstName="Ethan", email="<EMAIL>", error={}, file="email.go:497"
2025-07-31T10:35:40.819-04:00 level=ERROR msg=❌ Failed to send debt reminder email, firstName="Noah", email="<EMAIL>", error={}, file="email.go:497"
2025-07-31T10:35:40.82-04:00 level=INFO msg=📧 Sending debt reminder email, firstName="Isabella", email="<EMAIL>", amount=5000, dueDate="August 1, 2025", index=4
2025-07-31T10:35:40.822-04:00 level=WARN, value="defaultEmail not found in config", file="mail_utils.go:50"
2025-07-31T10:35:40.824-04:00 level=WARN, value="mailEngineList not configured correctly, fallback to empty list", file="mail_utils.go:90"
2025-07-31T10:35:40.825-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, ses="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.826-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, file="baseMailEngine.go:39", sesH="<EMAIL>"
2025-07-31T10:35:40.827-04:00 level=WARN msg=defaultEmail is not set for engine %s, use %s, mockmail="<EMAIL>", file="baseMailEngine.go:39"
2025-07-31T10:35:40.827-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-07-31T10:35:40.88-04:00 level=ERROR msg=❌ Failed to send debt reminder email, firstName="Isabella", email="<EMAIL>", error={}, file="email.go:497"
2025-07-31T10:35:40.919-04:00 level=INFO msg=✅ Successfully sent debt reminder email, email="<EMAIL>", firstName="Amelia"
2025-07-31T10:35:40.947-04:00 level=INFO msg=✅ Successfully sent debt reminder email, email="<EMAIL>", firstName="Jacob"
2025-07-31T10:35:40.947-04:00 level=INFO msg=✅ Successfully sent debt reminder email, firstName="Olivia", email="<EMAIL>"
2025-07-31T10:35:40.95-04:00 level=INFO msg=🎉 Completed sending debt reminder emails, landlordName="metro2 first name metro2 last name", tenantCount=12, successCount=9, errorCount=3
2025-07-31T10:35:40.987-04:00 level=INFO msg=✅ Successfully sent debt reminder email, firstName="xiaoou", email="<EMAIL>"
2025-07-31T10:35:40.988-04:00 level=INFO msg=🎉 Completed sending debt reminder emails, tenantCount=2, successCount=2, errorCount=0, landlordName="test1 test1"
