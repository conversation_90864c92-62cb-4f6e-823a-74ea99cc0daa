2025-07-31T10:30:56.504-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-07-31T10:30:56.519-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-07-31T10:30:56.527-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-07-31T10:30:56.529-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-07-31T10:30:56.531-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-07-31T10:30:56.532-04:00 level=INFO msg=🚀 Starting debt reminder scheduler..., sendDay=15, testMode=true, processIntervalMinutes=60, cleanupDays=30
2025-07-31T10:30:56.533-04:00 level=INFO msg=🚀 Debt reminder processing worker started, intervalMinutes=60, testMode=true, sendDay=15
2025-07-31T10:30:56.534-04:00 level=INFO, value="🧪 TEST MODE: Sending debt reminder emails immediately"
2025-07-31T10:30:56.536-04:00 level=INFO, value="📋 Processing debt reminder task"
2025-07-31T10:30:56.536-04:00 level=INFO, value="🔍 Searching for tenants with debt..."
2025-07-31T10:30:56.537-04:00 level=INFO, value="🔍 Querying active leases with debt..."
2025-07-31T10:30:56.54-04:00 level=INFO msg=📊 Found leases with debt, count=0
2025-07-31T10:30:56.541-04:00 level=INFO, value="📭 No tenants with debt found for reminder emails"
