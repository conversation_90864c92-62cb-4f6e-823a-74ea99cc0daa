package services

import (
	"context"
	"fmt"
	"rent_report/entities"
	"rent_report/utils"
	"strings"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

// Metro2NotificationConfig Metro2通知配置
type Metro2NotificationConfig struct {
	Enabled                bool
	DelayHours             int
	DelayMinutes           int
	ProcessIntervalMinutes int
	CleanupDays            int
}

// getMetro2NotificationConfig 获取Metro2通知配置
func getMetro2NotificationConfig() Metro2NotificationConfig {
	config := Metro2NotificationConfig{
		Enabled:                true,
		DelayHours:             24,
		DelayMinutes:           0,
		ProcessIntervalMinutes: 60,
		CleanupDays:            30,
	}

	// 读取配置
	if enabled := goconfig.Config("metro2NotificationEmail.enabled"); enabled != nil {
		if enabledBool, ok := enabled.(bool); ok {
			config.Enabled = enabledBool
		}
	}

	if delayHours := goconfig.Config("metro2NotificationEmail.delayHours"); delayHours != nil {
		if delayInt, ok := delayHours.(int); ok {
			config.DelayHours = delayInt
		} else if delayInt64, ok := delayHours.(int64); ok {
			config.DelayHours = int(delayInt64)
		}
	}

	if delayMinutes := goconfig.Config("metro2NotificationEmail.delayMinutes"); delayMinutes != nil {
		if delayInt, ok := delayMinutes.(int); ok {
			config.DelayMinutes = delayInt
		} else if delayInt64, ok := delayMinutes.(int64); ok {
			config.DelayMinutes = int(delayInt64)
		}
	}

	if processInterval := goconfig.Config("metro2NotificationEmail.processIntervalMinutes"); processInterval != nil {
		if intervalInt, ok := processInterval.(int); ok {
			config.ProcessIntervalMinutes = intervalInt
		} else if intervalInt64, ok := processInterval.(int64); ok {
			config.ProcessIntervalMinutes = int(intervalInt64)
		}
	}

	if cleanupDays := goconfig.Config("metro2NotificationEmail.cleanupDays"); cleanupDays != nil {
		if daysInt, ok := cleanupDays.(int); ok {
			config.CleanupDays = daysInt
		} else if daysInt64, ok := cleanupDays.(int64); ok {
			config.CleanupDays = int(daysInt64)
		}
	}

	return config
}

// StartMetro2NotificationScheduler 启动Metro2通知调度器
func StartMetro2NotificationScheduler() {
	config := getMetro2NotificationConfig()

	if !config.Enabled {
		golog.Info("Metro2 notification scheduler is disabled")
		return
	}

	golog.Info("Starting Metro2 notification scheduler...",
		"delayHours", config.DelayHours,
		"delayMinutes", config.DelayMinutes,
		"processIntervalMinutes", config.ProcessIntervalMinutes,
		"cleanupDays", config.CleanupDays)

	// 启动处理任务的goroutine
	go startMetro2NotificationWorker(config)

	// 启动清理任务的goroutine
	go startMetro2NotificationCleanupWorker(config)
}

// startMetro2NotificationWorker 启动Metro2通知处理工作器
func startMetro2NotificationWorker(config Metro2NotificationConfig) {
	golog.Info("Metro2 notification processing worker started", "intervalMinutes", config.ProcessIntervalMinutes)

	ticker := time.NewTicker(time.Duration(config.ProcessIntervalMinutes) * time.Minute)
	defer ticker.Stop()

	// 立即执行一次
	processMetro2NotificationTasks(config)

	// 然后按间隔执行
	for range ticker.C {
		processMetro2NotificationTasks(config)
	}
}

// startMetro2NotificationCleanupWorker 启动Metro2通知清理工作器
func startMetro2NotificationCleanupWorker(config Metro2NotificationConfig) {
	golog.Info("Metro2 notification cleanup worker started")

	// 每天执行一次清理
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		ctx := context.Background()
		err := entities.CleanupOldMetro2NotificationTasks(ctx, config.CleanupDays)
		if err != nil {
			golog.Error("Failed to cleanup old Metro2 notification tasks", "error", err)
		} else {
			golog.Info("Completed Metro2 notification tasks cleanup", "cleanupDays", config.CleanupDays)
		}
	}
}

// processMetro2NotificationTasks 处理Metro2通知任务
func processMetro2NotificationTasks(config Metro2NotificationConfig) {
	ctx := context.Background()

	golog.Info("Processing Metro2 notification tasks")

	// 获取待执行的任务
	tasks, err := entities.GetPendingMetro2NotificationTasks(ctx)
	if err != nil {
		golog.Error("Failed to get pending Metro2 notification tasks", "error", err)
		return
	}

	if len(tasks) == 0 {
		golog.Info("No pending Metro2 notification tasks found")
		return
	}

	golog.Info("Found pending Metro2 notification tasks", "count", len(tasks))

	// 处理每个任务
	for _, task := range tasks {
		err := processMetro2NotificationTask(ctx, &task)
		if err != nil {
			golog.Error("Failed to process Metro2 notification task",
				"taskId", task.ID,
				"metro2GenerationId", task.Metro2GenerationID,
				"reportMonth", task.ReportMonth,
				"error", err)

			// 标记任务完成但记录错误
			task.MarkCompleted(ctx, 0, err.Error())
		}
	}

	golog.Info("Completed Metro2 notification tasks processing", "processedCount", len(tasks))
}

// processMetro2NotificationTask 处理单个Metro2通知任务
func processMetro2NotificationTask(ctx context.Context, task *entities.Metro2NotificationTask) error {
	golog.Info("Processing Metro2 notification task",
		"taskId", task.ID,
		"metro2GenerationId", task.Metro2GenerationID,
		"reportMonth", task.ReportMonth,
		"scheduledTime", task.ScheduledTime)

	// 获取Metro2生成日志
	generationLog, err := entities.GetMetro2GenerationLogByID(ctx, task.Metro2GenerationID)
	if err != nil {
		return fmt.Errorf("failed to get Metro2 generation log: %v", err)
	}

	// 获取涉及的有欠款租约和租户
	tenants, landlordInfoMap, err := getTenantsWithDebtFromMetro2Report(ctx, generationLog)
	if err != nil {
		return fmt.Errorf("failed to get tenants with debt: %v", err)
	}

	if len(tenants) == 0 {
		golog.Info("No tenants with debt found for Metro2 notification",
			"metro2GenerationId", task.Metro2GenerationID,
			"reportMonth", task.ReportMonth)

		// 标记任务完成
		return task.MarkCompleted(ctx, 0, "")
	}

	// 按房东分组发送邮件
	emailsSent := 0
	for landlordId, landlordTenants := range groupTenantsByLandlord(tenants, landlordInfoMap) {
		landlordInfo := landlordInfoMap[landlordId]

		// 发送邮件
		utils.SendMetro2ReportedNotificationEmailsAsync(landlordTenants, landlordInfo, task.ReportMonth)
		emailsSent += len(landlordTenants)

		golog.Info("Sent Metro2 reported notification emails",
			"landlordId", landlordId,
			"tenantCount", len(landlordTenants),
			"reportMonth", task.ReportMonth)
	}

	// 标记任务完成
	err = task.MarkCompleted(ctx, emailsSent, "")
	if err != nil {
		golog.Error("Failed to mark Metro2 notification task as completed",
			"taskId", task.ID,
			"error", err)
		return err
	}

	golog.Info("Completed Metro2 notification task",
		"taskId", task.ID,
		"emailsSent", emailsSent,
		"reportMonth", task.ReportMonth)

	return nil
}

// CreateMetro2NotificationTask 创建Metro2通知任务
func CreateMetro2NotificationTask(ctx context.Context, generationLog *entities.Metro2GenerationLog) error {
	config := getMetro2NotificationConfig()

	if !config.Enabled {
		golog.Info("Metro2 notification is disabled, skipping task creation")
		return nil
	}

	// 计算执行时间
	delay := time.Duration(config.DelayHours)*time.Hour + time.Duration(config.DelayMinutes)*time.Minute
	scheduledTime := time.Now().Add(delay)

	// 创建任务
	task := &entities.Metro2NotificationTask{
		ID:                 utils.GenerateNanoID(),
		UserID:             generationLog.UserID,
		Metro2GenerationID: generationLog.ID,
		ReportMonth:        generationLog.ReportMonth,
		ScheduledTime:      scheduledTime,
		CreatedAt:          time.Now(),
		Completed:          false,
		EmailsSent:         0,
	}

	err := task.Create(ctx)
	if err != nil {
		return fmt.Errorf("failed to create Metro2 notification task: %v", err)
	}

	golog.Info("Created Metro2 notification task",
		"taskId", task.ID,
		"metro2GenerationId", generationLog.ID,
		"reportMonth", generationLog.ReportMonth,
		"scheduledTime", scheduledTime,
		"delayHours", config.DelayHours,
		"delayMinutes", config.DelayMinutes)

	return nil
}

// getTenantsWithDebtFromMetro2Report 从Metro2报告中获取有欠款的租户
func getTenantsWithDebtFromMetro2Report(ctx context.Context, generationLog *entities.Metro2GenerationLog) ([]utils.Metro2TenantEmailInfo, map[string]utils.LandlordInfo, error) {
	var tenants []utils.Metro2TenantEmailInfo
	landlordInfoMap := make(map[string]utils.LandlordInfo)

	// 遍历处理的租约
	for _, leaseInfo := range generationLog.ProcessedLeases {
		// 只处理有欠款的租约
		if leaseInfo.CurrentBalance <= 0 {
			continue
		}

		// 获取租约详细信息
		lease, err := entities.GetLeaseByID(ctx, leaseInfo.LeaseID)
		if err != nil {
			golog.Error("Failed to get lease for Metro2 notification",
				"leaseId", leaseInfo.LeaseID,
				"error", err)
			continue
		}

		// 获取房东信息（如果还没有获取过）
		if _, exists := landlordInfoMap[lease.UserID]; !exists {
			landlordInfo, err := getLandlordInfoForMetro2(ctx, lease.UserID, lease.PropertyID)
			if err != nil {
				golog.Error("Failed to get landlord info for Metro2 notification",
					"landlordId", lease.UserID,
					"propertyId", lease.PropertyID,
					"error", err)
				continue
			}
			landlordInfoMap[lease.UserID] = landlordInfo
		}

		// 获取当前租户信息
		for _, tenant := range lease.CurrentTenants {
			if tenant.Email != "" && tenant.FirstName != "" {
				// 格式化租金和余额
				rentAmount := fmt.Sprintf("%.2f", lease.RentAmount)
				remainingBalance := fmt.Sprintf("$%.2f", leaseInfo.CurrentBalance)

				tenants = append(tenants, utils.Metro2TenantEmailInfo{
					Email:            tenant.Email,
					FirstName:        tenant.FirstName,
					RentAmount:       rentAmount,
					RemainingBalance: remainingBalance,
				})
			}
		}
	}

	return tenants, landlordInfoMap, nil
}

// getLandlordInfoForMetro2 获取房东信息用于Metro2通知
func getLandlordInfoForMetro2(ctx context.Context, landlordId, propertyId string) (utils.LandlordInfo, error) {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, landlordId)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get user: %v", err)
	}

	// 获取属性信息
	property, err := entities.GetPropertyByID(ctx, propertyId, landlordId)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get property: %v", err)
	}

	// 构建地址字符串
	address := fmt.Sprintf("%s", property.Name)
	if property.Address.Street != "" {
		address = fmt.Sprintf("%s, %s", property.Name, property.Address.Street)
		if property.Address.City != "" {
			address = fmt.Sprintf("%s, %s", address, property.Address.City)
		}
	}

	// 从用户名中解析姓名（用户名格式通常是 "FirstName LastName"）
	firstName := ""
	lastName := ""
	if user.Username != "" {
		parts := strings.Fields(user.Username)
		if len(parts) >= 1 {
			firstName = parts[0]
		}
		if len(parts) >= 2 {
			lastName = strings.Join(parts[1:], " ")
		}
	}

	return utils.LandlordInfo{
		FirstName:       firstName,
		LastName:        lastName,
		PropertyAddress: address,
	}, nil
}

// groupTenantsByLandlord 按房东分组租户
func groupTenantsByLandlord(tenants []utils.Metro2TenantEmailInfo, landlordInfoMap map[string]utils.LandlordInfo) map[string][]utils.Metro2TenantEmailInfo {
	// 由于我们已经按房东获取了租户，这里简化处理
	// 实际上每个房东只会有一组租户
	result := make(map[string][]utils.Metro2TenantEmailInfo)

	// 如果只有一个房东，直接返回
	if len(landlordInfoMap) == 1 {
		for landlordId := range landlordInfoMap {
			result[landlordId] = tenants
			break
		}
	} else {
		// 如果有多个房东，需要更复杂的分组逻辑
		// 这里暂时简化处理，实际使用中可能需要更精确的分组
		for landlordId := range landlordInfoMap {
			result[landlordId] = tenants
			break // 暂时只处理第一个房东
		}
	}

	return result
}
