package services

import (
	"context"
	"fmt"
	"rent_report/entities"
	"rent_report/utils"
	"strings"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

// DebtReminderConfig 欠款提醒配置
type DebtReminderConfig struct {
	Enabled                bool
	SendDay                int
	TestMode               bool
	ProcessIntervalMinutes int
	CleanupDays            int
}

// getDebtReminderConfig 获取欠款提醒配置
func getDebtReminderConfig() DebtReminderConfig {
	config := DebtReminderConfig{
		Enabled:                true,
		SendDay:                15,
		TestMode:               false,
		ProcessIntervalMinutes: 60,
		CleanupDays:            30,
	}

	// 读取配置
	if enabled := goconfig.Config("debtReminderEmail.enabled"); enabled != nil {
		if enabledBool, ok := enabled.(bool); ok {
			config.Enabled = enabledBool
		}
	}

	if sendDay := goconfig.Config("debtReminderEmail.sendDay"); sendDay != nil {
		if dayInt, ok := sendDay.(int); ok {
			config.SendDay = dayInt
		} else if dayInt64, ok := sendDay.(int64); ok {
			config.SendDay = int(dayInt64)
		}
	}

	if testMode := goconfig.Config("debtReminderEmail.testMode"); testMode != nil {
		if testBool, ok := testMode.(bool); ok {
			config.TestMode = testBool
		}
	}

	if processInterval := goconfig.Config("debtReminderEmail.processIntervalMinutes"); processInterval != nil {
		if intervalInt, ok := processInterval.(int); ok {
			config.ProcessIntervalMinutes = intervalInt
		} else if intervalInt64, ok := processInterval.(int64); ok {
			config.ProcessIntervalMinutes = int(intervalInt64)
		}
	}

	if cleanupDays := goconfig.Config("debtReminderEmail.cleanupDays"); cleanupDays != nil {
		if daysInt, ok := cleanupDays.(int); ok {
			config.CleanupDays = daysInt
		} else if daysInt64, ok := cleanupDays.(int64); ok {
			config.CleanupDays = int(daysInt64)
		}
	}

	return config
}

// StartDebtReminderScheduler 启动欠款提醒调度器
func StartDebtReminderScheduler() {
	config := getDebtReminderConfig()

	if !config.Enabled {
		golog.Info("Debt reminder scheduler is disabled")
		return
	}

	golog.Info("Starting debt reminder scheduler...",
		"sendDay", config.SendDay,
		"testMode", config.TestMode,
		"processIntervalMinutes", config.ProcessIntervalMinutes,
		"cleanupDays", config.CleanupDays)

	// 启动处理任务的goroutine
	go startDebtReminderWorker(config)
}

// startDebtReminderWorker 启动欠款提醒处理工作器
func startDebtReminderWorker(config DebtReminderConfig) {
	golog.Info("Debt reminder processing worker started", "intervalMinutes", config.ProcessIntervalMinutes)

	// 如果是测试模式，立即执行一次
	if config.TestMode {
		golog.Info("Test mode enabled, sending debt reminder emails immediately")
		processDebtReminderTask(config)
	}

	// 然后按间隔执行
	ticker := time.NewTicker(time.Duration(config.ProcessIntervalMinutes) * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		if !config.TestMode {
			// 生产模式：检查是否是发送日期
			now := time.Now()
			if now.Day() == config.SendDay {
				// 检查今天是否已经发送过（简单的防重复机制）
				if !hasAlreadySentToday(now) {
					processDebtReminderTask(config)
					markSentToday(now)
				}
			}
		}
	}
}

// processDebtReminderTask 处理欠款提醒任务
func processDebtReminderTask(config DebtReminderConfig) {
	ctx := context.Background()

	golog.Info("Processing debt reminder task")

	// 获取所有有欠款的租户（已按房东分组）
	tenantsByLandlord, landlordInfoMap, err := getTenantsWithDebt(ctx)
	if err != nil {
		golog.Error("Failed to get tenants with debt", "error", err)
		return
	}

	if len(tenantsByLandlord) == 0 {
		golog.Info("No tenants with debt found for reminder emails")
		return
	}

	golog.Info("Found tenants with debt", "landlordCount", len(tenantsByLandlord))

	// 直接遍历已分组的租户发送邮件
	totalEmailsSent := 0
	for landlordId, landlordTenants := range tenantsByLandlord {
		landlordInfo := landlordInfoMap[landlordId]

		// 发送邮件
		utils.SendDebtReminderEmailsAsync(landlordTenants, landlordInfo)
		totalEmailsSent += len(landlordTenants)

		golog.Info("Sent debt reminder emails",
			"landlordId", landlordId,
			"tenantCount", len(landlordTenants))
	}

	golog.Info("Completed debt reminder task", "totalEmailsSent", totalEmailsSent)
}

// getTenantsWithDebt 获取所有有欠款的租户，按房东分组
func getTenantsWithDebt(ctx context.Context) (map[string][]utils.DebtTenantEmailInfo, map[string]utils.LandlordInfo, error) {
	tenantsByLandlord := make(map[string][]utils.DebtTenantEmailInfo)
	landlordInfoMap := make(map[string]utils.LandlordInfo)

	// 查询所有有欠款的活跃租约
	leases, err := entities.GetActiveLeasesWithDebt(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get active leases with debt: %v", err)
	}

	// 遍历租约
	for _, lease := range leases {
		// 获取房东信息（如果还没有获取过）
		if _, exists := landlordInfoMap[lease.UserID]; !exists {
			landlordInfo, err := getLandlordInfoForDebtReminder(ctx, lease.UserID, lease.PropertyID)
			if err != nil {
				golog.Error("Failed to get landlord info for debt reminder",
					"landlordId", lease.UserID,
					"propertyId", lease.PropertyID,
					"error", err)
				continue
			}
			landlordInfoMap[lease.UserID] = landlordInfo
		}

		// 获取属性信息用于地址
		property, err := entities.GetPropertyByID(ctx, lease.PropertyID, lease.UserID)
		if err != nil {
			golog.Error("Failed to get property for debt reminder",
				"propertyId", lease.PropertyID,
				"error", err)
			continue
		}

		// 构建属性地址
		propertyAddress := property.Name
		if property.Address.Street != "" {
			propertyAddress = fmt.Sprintf("%s, %s", property.Name, property.Address.Street)
			if property.Address.City != "" {
				propertyAddress = fmt.Sprintf("%s, %s", propertyAddress, property.Address.City)
			}
		}

		// 计算到期日期
		dueDate := calculateDueDate(lease.RentDueDay)

		// 获取当前租户信息，直接按房东分组
		for _, tenant := range lease.CurrentTenants {
			if tenant.Email != "" && tenant.FirstName != "" {
				// 格式化租金
				rentAmount := fmt.Sprintf("%.2f", lease.RentAmount)

				tenantInfo := utils.DebtTenantEmailInfo{
					Email:           tenant.Email,
					FirstName:       tenant.FirstName,
					RentAmount:      rentAmount,
					DueDate:         dueDate,
					PropertyAddress: propertyAddress,
				}

				// 直接按房东ID分组
				tenantsByLandlord[lease.UserID] = append(tenantsByLandlord[lease.UserID], tenantInfo)
			}
		}
	}

	return tenantsByLandlord, landlordInfoMap, nil
}

// getLandlordInfoForDebtReminder 获取房东信息用于欠款提醒
func getLandlordInfoForDebtReminder(ctx context.Context, landlordId, propertyId string) (utils.LandlordInfo, error) {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, landlordId)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get user: %v", err)
	}

	// 从用户名中解析姓名（用户名格式通常是 "FirstName LastName"）
	firstName := ""
	lastName := ""
	if user.Username != "" {
		parts := strings.Fields(user.Username)
		if len(parts) >= 1 {
			firstName = parts[0]
		}
		if len(parts) >= 2 {
			lastName = strings.Join(parts[1:], " ")
		}
	}

	return utils.LandlordInfo{
		FirstName:       firstName,
		LastName:        lastName,
		PropertyAddress: "", // 不需要在这里设置，每个租户有自己的属性地址
	}, nil
}

// calculateDueDate 计算到期日期字符串
func calculateDueDate(rentDueDay int) string {
	now := time.Now()
	
	// 构建当月的到期日期
	dueDate := time.Date(now.Year(), now.Month(), rentDueDay, 0, 0, 0, 0, now.Location())
	
	// 如果到期日已过，显示下个月的到期日
	if dueDate.Before(now) {
		dueDate = dueDate.AddDate(0, 1, 0)
	}
	
	return dueDate.Format("January 2, 2006")
}

// hasAlreadySentToday 检查今天是否已经发送过（简单实现）
func hasAlreadySentToday(date time.Time) bool {
	// 简单的防重复机制：检查是否在同一天内已经发送过
	// 这里可以用文件或内存缓存实现，为了简单起见，暂时返回false
	// 在实际生产环境中，可以使用数据库记录或文件标记
	return false
}

// markSentToday 标记今天已发送（简单实现）
func markSentToday(date time.Time) {
	// 简单的防重复机制：标记今天已发送
	// 这里可以用文件或数据库记录实现
	golog.Info("Marked debt reminder as sent for today", "date", date.Format("2006-01-02"))
}
