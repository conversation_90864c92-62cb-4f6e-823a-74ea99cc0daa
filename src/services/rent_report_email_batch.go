package services

import (
	"context"
	"fmt"
	"rent_report/entities"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

// RentReportEmailBatchConfig 租金报告邮件批处理配置
type RentReportEmailBatchConfig struct {
	Enabled                bool
	ProcessIntervalMinutes int // 处理间隔（分钟）
	ProcessWindowMinutes   int // 处理窗口（分钟）
	CleanupDays            int // 清理天数
}

// getRentReportEmailBatchConfig 获取租金报告邮件批处理配置
func getRentReportEmailBatchConfig() RentReportEmailBatchConfig {
	config := RentReportEmailBatchConfig{
		Enabled:                true,
		ProcessIntervalMinutes: 180, // 默认每180分钟（3小时）处理一次
		ProcessWindowMinutes:   120, // 默认处理前120分钟（2小时）的记录
		CleanupDays:            7,   // 默认清理7天前的记录
	}

	// 从配置文件读取
	if enabled := goconfig.Config("rentReportEmailBatch.enabled"); enabled != nil {
		if enabledBool, ok := enabled.(bool); ok {
			config.Enabled = enabledBool
		}
	}

	// 优先读取分钟级配置（测试用）
	if processInterval := goconfig.Config("rentReportEmailBatch.processIntervalMinutes"); processInterval != nil {
		if intervalInt, ok := processInterval.(int); ok {
			config.ProcessIntervalMinutes = intervalInt
		}
	} else if processInterval := goconfig.Config("rentReportEmailBatch.processIntervalHours"); processInterval != nil {
		// 兼容小时级配置
		if intervalInt, ok := processInterval.(int); ok {
			config.ProcessIntervalMinutes = intervalInt * 60
		}
	}

	if processWindow := goconfig.Config("rentReportEmailBatch.processWindowMinutes"); processWindow != nil {
		if windowInt, ok := processWindow.(int); ok {
			config.ProcessWindowMinutes = windowInt
		}
	} else if processWindow := goconfig.Config("rentReportEmailBatch.processWindowHours"); processWindow != nil {
		// 兼容小时级配置
		if windowInt, ok := processWindow.(int); ok {
			config.ProcessWindowMinutes = windowInt * 60
		}
	}

	if cleanupDays := goconfig.Config("rentReportEmailBatch.cleanupDays"); cleanupDays != nil {
		if daysInt, ok := cleanupDays.(int); ok {
			config.CleanupDays = daysInt
		}
	}

	return config
}

// StartRentReportEmailBatchScheduler 启动租金报告邮件批处理调度器
func StartRentReportEmailBatchScheduler() {
	config := getRentReportEmailBatchConfig()

	if !config.Enabled {
		golog.Info("Rent report email batch scheduler is disabled")
		return
	}

	golog.Info("Starting rent report email batch scheduler...",
		"processIntervalMinutes", config.ProcessIntervalMinutes,
		"processWindowMinutes", config.ProcessWindowMinutes,
		"cleanupDays", config.CleanupDays)

	// 启动处理任务的goroutine
	go startBatchProcessingWorker(config)

	// 启动清理任务的goroutine
	go startBatchCleanupWorker(config)
}

// startBatchProcessingWorker 启动批处理工作器
func startBatchProcessingWorker(config RentReportEmailBatchConfig) {
	ticker := time.NewTicker(time.Duration(config.ProcessIntervalMinutes) * time.Minute)
	defer ticker.Stop()

	golog.Info("Rent report email batch processing worker started",
		"intervalMinutes", config.ProcessIntervalMinutes)

	// 立即执行一次
	processBatch(config.ProcessWindowMinutes)

	// 然后定期执行
	for range ticker.C {
		processBatch(config.ProcessWindowMinutes)
	}
}

// startBatchCleanupWorker 启动清理工作器
func startBatchCleanupWorker(config RentReportEmailBatchConfig) {
	ticker := time.NewTicker(24 * time.Hour) // 每天清理一次
	defer ticker.Stop()

	golog.Info("Rent report email batch cleanup worker started")

	for range ticker.C {
		cleanupOldRecords(config.CleanupDays)
	}
}

// processBatch 处理批次邮件
func processBatch(windowMinutes int) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	golog.Info("Processing rent report email batch", "windowMinutes", windowMinutes)

	// 获取未处理的记录
	records, err := entities.GetUnprocessedRentReportEmailQueue(ctx, windowMinutes)
	if err != nil {
		golog.Error("Failed to get unprocessed records", "error", err)
		return
	}

	if len(records) == 0 {
		golog.Debug("No unprocessed rent report email records found")
		return
	}

	golog.Info("Found unprocessed rent report email records", "count", len(records))

	// 处理每个记录
	for _, record := range records {
		err := processEmailRecord(ctx, &record)
		if err != nil {
			golog.Error("Failed to process email record",
				"error", err,
				"recordId", record.ID,
				"tenantId", record.TntId,
				"tenantEmail", record.TntEmail)
			continue
		}

		// 标记为已处理
		err = entities.MarkRentReportEmailQueueProcessed(ctx, record.ID)
		if err != nil {
			golog.Error("Failed to mark record as processed",
				"error", err,
				"recordId", record.ID)
		}
	}

	golog.Info("Completed rent report email batch processing", "processedCount", len(records))
}

// processEmailRecord 处理单个邮件记录
func processEmailRecord(ctx context.Context, record *entities.RentReportEmailQueue) error {
	// 根据最终状态决定发送什么邮件
	switch record.FinalStatus {
	case "enabled":
		return sendRentReportingEnabledEmail(ctx, record)
	case "disabled", "not_exist":
		return sendRentReportingPausedEmail(ctx, record)
	default:
		golog.Warn("Unknown final status, skipping email",
			"finalStatus", record.FinalStatus,
			"tenantId", record.TntId)
		return nil
	}
}

// sendRentReportingEnabledEmail 发送租金报告启用邮件
func sendRentReportingEnabledEmail(ctx context.Context, record *entities.RentReportEmailQueue) error {
	// 获取房东和属性信息
	landlordInfo, err := getLandlordInfo(ctx, record.LandlordId, record.PropId)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 准备租户邮件信息
	tenants := []utils.TenantEmailInfo{
		{
			Email:     record.TntEmail,
			FirstName: record.TntFirstName,
		},
	}

	// 发送启用邮件
	utils.SendRentReportingNotificationEmailsAsync(tenants, landlordInfo)

	golog.Info("Sent rent reporting enabled email",
		"tenantEmail", record.TntEmail,
		"tenantId", record.TntId,
		"leaseId", record.LeaseId)

	return nil
}

// sendRentReportingPausedEmail 发送租金报告暂停邮件
func sendRentReportingPausedEmail(ctx context.Context, record *entities.RentReportEmailQueue) error {
	// 获取房东和属性信息
	landlordInfo, err := getLandlordInfo(ctx, record.LandlordId, record.PropId)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 准备租户邮件信息
	tenants := []utils.TenantEmailInfo{
		{
			Email:     record.TntEmail,
			FirstName: record.TntFirstName,
		},
	}

	// 发送暂停邮件
	utils.SendRentReportingPausedNotificationEmailsAsync(tenants, landlordInfo)

	golog.Info("Sent rent reporting paused email",
		"tenantEmail", record.TntEmail,
		"tenantId", record.TntId,
		"leaseId", record.LeaseId)

	return nil
}

// getLandlordInfo 获取房东和属性信息
func getLandlordInfo(ctx context.Context, landlordId, propId string) (utils.LandlordInfo, error) {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, landlordId)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := entities.GetProperty(ctx, propId, landlordId)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", propId)
		// 不返回错误，使用默认值
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,
		LastName:        "",
		PropertyAddress: "your property",
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	return landlordInfo, nil
}

// cleanupOldRecords 清理旧记录
func cleanupOldRecords(daysOld int) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	golog.Info("Cleaning up old rent report email records", "daysOld", daysOld)

	err := entities.CleanupOldRentReportEmailQueue(ctx, daysOld)
	if err != nil {
		golog.Error("Failed to cleanup old records", "error", err)
		return
	}

	golog.Info("Completed cleanup of old rent report email records")
}
