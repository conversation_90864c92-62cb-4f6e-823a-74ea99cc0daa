package main

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
)

// 完整的RESTful API路由测试
func TestAllRESTfulRoutes(t *testing.T) {
	gin.SetMode(gin.TestMode)
	r := gin.New()

	// 添加所有新的RESTful路由
	r.GET("/v1/referral-codes/:code/validation", func(c *gin.Context) {
		c.J<PERSON>(http.StatusOK, gin.H{"message": "new referral validation works"})
	})

	r.POST("/v1/referral-code-applications", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "new referral application works"})
	})

	r.GET("/v1/documents/:fileId", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "new file download works"})
	})

	r.DELETE("/v1/subscriptions/:id", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{"message": "new subscription cancel works"})
	})

	r.PUT("/v1/leases/:leaseId/tenants/:tenantId", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "new tenant update works"})
	})

	r.DELETE("/v1/leases/:leaseId/tenants/:tenantId", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "new tenant delete works"})
	})

	r.PUT("/v1/leases/:leaseId/tenants/self", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "new tenant self update works"})
	})

	r.PUT("/v1/invitations/:code/status", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "new invitation status update works"})
	})

	// 添加所有旧的端点（向后兼容）
	r.POST("/v1/referral-codes/validate", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "old referral validation works"})
	})

	r.POST("/v1/referral-codes/apply", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "old referral application works"})
	})

	r.GET("/v1/leases/download/:fileId", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "old file download works"})
	})

	r.POST("/v1/usersub/cancel", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "old subscription cancel works"})
	})

	r.PUT("/v1/leases/:leaseId/ctnts/:tenantId", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "old tenant update works"})
	})

	r.DELETE("/v1/leases/:leaseId/ctnts/:tenantId", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "old tenant delete works"})
	})

	r.PUT("/v1/leases/:leaseId/ctnts/self_id", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "old tenant self update works"})
	})

	r.POST("/v1/invitations/update_status_by_code", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "old invitation status update works"})
	})

	// 测试所有路由
	routes := r.Routes()
	fmt.Printf("总共注册了 %d 个路由:\n", len(routes))

	expectedRoutes := map[string]string{
		// 新的RESTful端点
		"GET /v1/referral-codes/:code/validation":      "新推荐码验证",
		"POST /v1/referral-code-applications":          "新推荐码应用",
		"GET /v1/documents/:fileId":                    "新文件下载",
		"DELETE /v1/subscriptions/:id":                 "新订阅取消",
		"PUT /v1/leases/:leaseId/tenants/:tenantId":    "新租客更新",
		"DELETE /v1/leases/:leaseId/tenants/:tenantId": "新租客删除",
		"PUT /v1/leases/:leaseId/tenants/self":         "新租客自更新",
		"PUT /v1/invitations/:code/status":             "新邀请状态更新",

		// 旧的端点（向后兼容）
		"POST /v1/referral-codes/validate":           "旧推荐码验证",
		"POST /v1/referral-codes/apply":              "旧推荐码应用",
		"GET /v1/leases/download/:fileId":            "旧文件下载",
		"POST /v1/usersub/cancel":                    "旧订阅取消",
		"PUT /v1/leases/:leaseId/ctnts/:tenantId":    "旧租客更新",
		"DELETE /v1/leases/:leaseId/ctnts/:tenantId": "旧租客删除",
		"PUT /v1/leases/:leaseId/ctnts/self_id":      "旧租客自更新",
		"POST /v1/invitations/update_status_by_code": "旧邀请状态更新",
	}

	foundRoutes := make(map[string]bool)
	for _, route := range routes {
		routeKey := route.Method + " " + route.Path
		foundRoutes[routeKey] = true
	}

	fmt.Println("\n=== RESTful API 路由检查结果 ===")
	fmt.Println("\n新的RESTful端点:")
	newEndpoints := []string{
		"GET /v1/referral-codes/:code/validation",
		"POST /v1/referral-code-applications",
		"GET /v1/documents/:fileId",
		"DELETE /v1/subscriptions/:id",
		"PUT /v1/leases/:leaseId/tenants/:tenantId",
		"DELETE /v1/leases/:leaseId/tenants/:tenantId",
		"PUT /v1/leases/:leaseId/tenants/self",
		"PUT /v1/invitations/:code/status",
	}

	newFound := 0
	for _, endpoint := range newEndpoints {
		if foundRoutes[endpoint] {
			fmt.Printf("✅ %s - %s\n", endpoint, expectedRoutes[endpoint])
			newFound++
		} else {
			fmt.Printf("❌ %s - %s (未找到)\n", endpoint, expectedRoutes[endpoint])
		}
	}

	fmt.Println("\n旧的端点（向后兼容）:")
	oldEndpoints := []string{
		"POST /v1/referral-codes/validate",
		"POST /v1/referral-codes/apply",
		"GET /v1/leases/download/:fileId",
		"POST /v1/usersub/cancel",
		"PUT /v1/leases/:leaseId/ctnts/:tenantId",
		"DELETE /v1/leases/:leaseId/ctnts/:tenantId",
		"PUT /v1/leases/:leaseId/ctnts/self_id",
		"POST /v1/invitations/update_status_by_code",
	}

	oldFound := 0
	for _, endpoint := range oldEndpoints {
		if foundRoutes[endpoint] {
			fmt.Printf("✅ %s - %s\n", endpoint, expectedRoutes[endpoint])
			oldFound++
		} else {
			fmt.Printf("❌ %s - %s (未找到)\n", endpoint, expectedRoutes[endpoint])
		}
	}

	fmt.Printf("\n=== 测试总结 ===\n")
	fmt.Printf("新RESTful端点: %d/%d 通过\n", newFound, len(newEndpoints))
	fmt.Printf("旧端点(兼容): %d/%d 通过\n", oldFound, len(oldEndpoints))
	fmt.Printf("总计: %d/%d 通过\n", newFound+oldFound, len(expectedRoutes))

	if newFound == len(newEndpoints) && oldFound == len(oldEndpoints) {
		fmt.Println("🎉 所有API端点都已正确注册!")
	} else {
		fmt.Println("⚠️  有些端点未找到")
		t.Fail()
	}
}
